#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
from datetime import datetime, timezone
import logging

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_json_processing():
    """测试JSON处理"""
    perception_data = []
    
    with open('data/save_1753355915725.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    logger.info(f"读取到 {len(lines)} 行数据")
    
    for line_num, line in enumerate(lines[:5], 1):  # 只处理前5行
        line = line.strip()
        if not line:
            continue
            
        try:
            # 清理可能的空字符和其他控制字符
            clean_line = line.replace('\x00', '').replace('\r', '')
            logger.debug(f"第{line_num}行清理后长度: {len(clean_line)}")
            
            data = json.loads(clean_line)
            logger.debug(f"第{line_num}行JSON解析成功")
            
            # 支持新格式和旧格式
            if 'timestamp' in data and 'object_result' in data:
                logger.debug(f"第{line_num}行：识别为新格式")
                # 新格式：毫秒时间戳
                timestamp_ms = data['timestamp']
                timestamp_dt = datetime.fromtimestamp(timestamp_ms / 1000.0, timezone.utc)
                timestamp_str = timestamp_dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                obj_list = data['object_result']
                logger.debug(f"处理新格式数据，时间戳: {timestamp_str}, 对象数量: {len(obj_list)}")
                
                for obj in obj_list:
                    # 支持新格式和旧格式的对象数据
                    if 'id' in obj and 'lat' in obj and 'lon' in obj:
                        # 新格式：直接使用字段值，速度已经是m/s
                        logger.debug(f"处理新格式对象: {obj.get('id', 'unknown')}")
                        try:
                            record = {
                                'timestamp': timestamp_str,
                                'id': str(obj['id']),
                                'lat': float(obj['lat']),
                                'lon': float(obj['lon']),
                                'speed': float(obj.get('speed', 0.0)),  # 已经是m/s
                                'heading': float(obj.get('heading', 0.0)),
                                'altitude': float(obj.get('ele', 0.0)),
                                'vehicle_class': int(obj.get('veh_type', 0)),
                                'ptc_type': int(obj.get('type', 1)),
                                'vehicle_height': float(obj.get('height', 0.0)),
                                'vehicle_width': float(obj.get('width', 0.0)),
                                'vehicle_length': float(obj.get('length', 0.0))
                            }
                            perception_data.append(record)
                            logger.debug(f"成功添加新格式记录: {record['id']}")
                        except Exception as obj_e:
                            logger.warning(f"处理新格式对象时出错: {obj_e}, 对象: {obj}")
                            continue
                    elif all(key in obj for key in ['ID', 'PtcLat', 'PtcLon']):
                        logger.debug(f"处理旧格式对象: {obj.get('ID', 'unknown')}")
                        # 旧格式：转换速度单位：km/h -> m/s
                        speed_kmh = float(obj.get('PtcSpeed', 0.0))
                        speed_ms = speed_kmh / 3.6  # km/h 转换为 m/s

                        record = {
                            'timestamp': timestamp_str,
                            'id': str(obj['ID']),
                            'lat': float(obj['PtcLat']),
                            'lon': float(obj['PtcLon']),
                            'speed': speed_ms,
                            'heading': float(obj.get('PtcHeading', 0.0)),
                            'altitude': float(obj.get('PtcEle', 0.0)),
                            'vehicle_class': int(obj.get('vehicleClass', 0)),
                            'ptc_type': int(obj.get('PtcType', 1)),
                            'vehicle_height': float(obj.get('VehH', 0.0)),
                            'vehicle_width': float(obj.get('VehW', 0.0)),
                            'vehicle_length': float(obj.get('VehL', 0.0))
                        }
                        perception_data.append(record)
                        logger.debug(f"成功添加旧格式记录: {record['id']}")
                    else:
                        logger.warning(f"对象字段不匹配，跳过: {list(obj.keys())[:5]}")
                        
            elif 'Timestamp' in data and 'Obj_List' in data:
                logger.debug(f"第{line_num}行：识别为旧格式")
                # 旧格式
                timestamp_str = data['Timestamp']
                obj_list = data['Obj_List']
            else:
                logger.warning(f"第{line_num}行：未识别的JSON格式，跳过")
                continue
                
        except Exception as e:
            logger.warning(f"处理第{line_num}行时出错: {e}")
            logger.debug(f"问题行内容: {line[:100]}...")
            continue
    
    logger.info(f"JSON数据处理完成，共{len(perception_data)}条记录")
    return perception_data

if __name__ == '__main__':
    result = test_json_processing()
    print(f"处理结果：{len(result)}条记录")
    if result:
        print("第一条记录：", result[0])

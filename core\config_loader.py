"""
统一配置加载器
支持分层和扁平两种配置格式，实现向后兼容
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class Config:
    """统一配置类"""
    
    def __init__(self, config_data: Dict[str, Any]):
        self._data = config_data
        self._flatten_data = self._flatten_config(config_data)
    
    def _flatten_config(self, data: Dict[str, Any], prefix: str = '') -> Dict[str, Any]:
        """将分层配置拍平为扁平结构"""
        flattened = {}
        
        for key, value in data.items():
            if prefix:
                full_key = f"{prefix}.{key}"
            else:
                full_key = key
            
            if isinstance(value, dict) and key not in ['profiles']:  # profiles不拍平
                flattened.update(self._flatten_config(value, full_key))
            else:
                flattened[key] = value
                if prefix:  # 同时保存完整路径
                    flattened[full_key] = value
        
        return flattened
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持层级访问和扁平访问"""
        # 先尝试直接访问
        if key in self._flatten_data:
            return self._flatten_data[key]
        
        # 尝试层级访问
        keys = key.split('.')
        current = self._data
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default
    
    def __getattr__(self, name: str) -> Any:
        """支持属性访问方式"""
        # 属性映射 - 将旧的扁平属性名映射到新的层级结构
        attribute_mapping = {
            'corridor_enabled': 'corridor.enabled',
            'corridor_fallback_to_roi': 'corridor.fallback_to_roi',
            'corridor_downsample_interval_meters': 'corridor.downsample_interval_meters',
            'corridor_long_buffer_meters': 'corridor.long_buffer_meters',
            'corridor_lat_buffer_meters': 'corridor.lat_buffer_meters',
            'corridor_time_buffer_seconds': 'corridor.time_buffer_seconds',
            'corridor_min_trajectory_points': 'corridor.min_trajectory_points',
            'corridor_min_trajectory_duration': 'corridor.min_trajectory_duration',
            'corridor_direction_threshold_degrees': 'corridor.direction_threshold_degrees',
            'corridor_point_level_filter': 'corridor.point_level_filter',
            'roi_long': 'roi.roi_long',
            'roi_lat': 'roi.roi_lat',
            'win_sec': 'matching.win_sec',
            'local_match_thr': 'matching.local_match_thr',
            'split_match_thr': 'matching.split_match_thr',
            'overlap_min': 'matching.overlap_min',
            'max_gap': 'matching.max_gap',
            'gap_match_thr': 'matching.gap_match_thr',
            'max_missing_gap': 'matching.max_missing_gap',
            'min_missing_gap': 'matching.min_missing_gap',
            'rtk_buffer': 'matching.rtk_buffer',
            'good_match_thr': 'matching.good_match_thr',
            'min_segment_length': 'matching.min_segment_length',
            'switch_dt': 'anomaly.switch_dt',
            'switch_dist': 'anomaly.switch_dist',
            'switch_speed': 'anomaly.switch_speed',
            'switch_heading': 'anomaly.switch_heading',
            'peak_weight': 'scoring.peak_weight',
            'duration_weight': 'scoring.duration_weight',
            'stability_weight': 'scoring.stability_weight'
        }
        
        # 如果有映射，使用映射的路径
        if name in attribute_mapping:
            mapped_key = attribute_mapping[name]
            value = self.get(mapped_key)
        else:
            value = self.get(name)
        
        if value is None:
            raise AttributeError(f"配置中没有找到属性: {name}")
        return value
    
    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return self.get(key) is not None
    
    def apply_profile(self, profile_name: str):
        """应用配置文件"""
        if 'profiles' not in self._data:
            logger.warning(f"配置中没有profiles部分，无法应用profile: {profile_name}")
            return
        
        if profile_name not in self._data['profiles']:
            logger.warning(f"找不到profile: {profile_name}")
            return
        
        profile = self._data['profiles'][profile_name]
        logger.info(f"应用配置文件: {profile_name} - {profile.get('description', '')}")
        
        # 应用profile中的设置
        for key, value in profile.items():
            if key != 'description':
                # 更新到对应的层级
                if '.' in key:
                    keys = key.split('.')
                    current = self._data
                    for k in keys[:-1]:
                        if k not in current:
                            current[k] = {}
                        current = current[k]
                    current[keys[-1]] = value
                else:
                    # 尝试找到合适的位置
                    self._update_nested_value(key, value)
        
        # 重新拍平配置
        self._flatten_data = self._flatten_config(self._data)
    
    def _update_nested_value(self, key: str, value: Any):
        """更新嵌套配置值"""
        # 常见的配置映射
        config_mapping = {
            'gap_match_thr': 'matching.gap_match_thr',
            'rtk_buffer': 'matching.rtk_buffer'
        }
        
        if key in config_mapping:
            nested_key = config_mapping[key]
            keys = nested_key.split('.')
            current = self._data
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            current[keys[-1]] = value
        else:
            # 直接设置到根级别
            self._flatten_data[key] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """返回完整配置字典"""
        return self._data.copy()
    
    def to_flat_dict(self) -> Dict[str, Any]:
        """返回拍平的配置字典"""
        return self._flatten_data.copy()

class ConfigLoader:
    """配置加载器"""
    
    @staticmethod
    def load_config(config_path: str, profile: Optional[str] = None) -> Config:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            profile: 要应用的配置文件名
            
        Returns:
            Config: 统一配置对象
        """
        # 检查文件是否存在
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        # 加载JSON配置
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {config_path}, 错误: {e}")
        
        # 检测配置格式
        config_format = ConfigLoader._detect_config_format(config_data)
        logger.info(f"加载配置文件: {config_path} (格式: {config_format})")
        
        # 如果是扁平格式，转换为分层格式
        if config_format == 'flat':
            config_data = ConfigLoader._convert_flat_to_nested(config_data)
        
        # 创建配置对象 - Config类期望字典参数
        config = Config(config_data)
        
        # 应用profile（如果指定）
        if profile:
            config.apply_profile(profile)
        
        return config
    
    @staticmethod
    def _detect_config_format(config_data: Dict[str, Any]) -> str:
        """检测配置格式"""
        # 检查是否有分层结构的关键字段
        nested_keys = ['roi', 'matching', 'scoring', 'anomaly']
        has_nested = any(key in config_data for key in nested_keys)
        
        if has_nested:
            return 'nested'
        else:
            return 'flat'
    
    @staticmethod
    def _convert_flat_to_nested(flat_config: Dict[str, Any]) -> Dict[str, Any]:
        """将扁平配置转换为分层配置"""
        nested = {
            'description': '从扁平格式转换的配置',
            'version': '1.0',
            'roi': {},
            'matching': {},
            'scoring': {},
            'anomaly': {},
            'processing': {
                'time_sync_enabled': True,
                'spatial_filter_enabled': True,
                'data_validation_enabled': True,
                'auto_format_detection': True
            },
            'output': {
                'generate_csv': True,
                'generate_json': True,
                'generate_plots': False,
                'include_debug_info': True
            }
        }
        
        # 映射扁平配置到分层结构
        field_mapping = {
            # ROI相关
            'roi_long': 'roi.roi_long',
            'roi_lat': 'roi.roi_lat',
            
            # 匹配算法相关
            'win_sec': 'matching.win_sec',
            'local_match_thr': 'matching.local_match_thr',
            'split_match_thr': 'matching.split_match_thr',
            
            # 匹配相关
            'overlap_min': 'matching.overlap_min',
            'max_gap': 'matching.max_gap',
            'gap_match_thr': 'matching.gap_match_thr',
            'max_missing_gap': 'matching.max_missing_gap',
            'min_missing_gap': 'matching.min_missing_gap',
            'rtk_buffer': 'matching.rtk_buffer',
            'good_match_thr': 'matching.good_match_thr',
            'min_segment_length': 'matching.min_segment_length',
            
            # 评分相关
            'peak_weight': 'scoring.peak_weight',
            'duration_weight': 'scoring.duration_weight',
            'stability_weight': 'scoring.stability_weight',
            
            # 异常检测相关
            'switch_dt': 'anomaly.switch_dt',
            'switch_dist': 'anomaly.switch_dist',
            'switch_speed': 'anomaly.switch_speed',
            'switch_heading': 'anomaly.switch_heading'
        }
        
        # 应用映射
        for flat_key, value in flat_config.items():
            if flat_key in field_mapping:
                nested_path = field_mapping[flat_key]
                keys = nested_path.split('.')
                current = nested
                for key in keys[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                current[keys[-1]] = value
            else:
                # 未知字段保留在根级别
                nested[flat_key] = value
        
        return nested

def load_config(config_path: str = 'config/unified_config.json', profile: Optional[str] = None) -> Config:
    """
    便捷的配置加载函数
    
    Args:
        config_path: 配置文件路径，默认使用统一配置文件
        profile: 要应用的配置文件名
        
    Returns:
        Config: 统一配置对象
    """
    return ConfigLoader.load_config(config_path, profile)

# 向后兼容的配置类
class LegacyConfig:
    """向后兼容的配置类，模拟原来的简单配置对象"""
    
    def __init__(self, config: Config):
        self._config = config
        # 将所有配置项暴露为属性
        for key, value in config.to_flat_dict().items():
            setattr(self, key, value)
    
    def __getattr__(self, name: str) -> Any:
        """首先尝试使用内部 Config 的属性解析（支持映射），
        若抛出 AttributeError 再回退到直接 get，保持兼容性"""
        try:
            # 先走 Config.__getattr__，其中包含了丰富的属性映射规则
            return getattr(self._config, name)
        except AttributeError:
            # 如果 Config 无对应映射，再尝试 get；最后仍抛出异常以保持原语义
            value = self._config.get(name)
            if value is None:
                raise
            return value
    
    @property
    def __dict__(self):
        return self._config.to_flat_dict()
    
    def to_flat_dict(self):
        """返回扁平配置字典"""
        return self._config.to_flat_dict() 
# 轨迹匹配性能优化方案

> **版本**: v1.0  
> **日期**: 2025-01-30  
> **目标**: 解决核心链构建阶段的性能瓶颈，将总耗时从 26s 压缩到 5-7s

---

## 📊 当前性能瓶颈分析

### 耗时分布（基于 Profile 结果）

| 模块 | 耗时 | 占比 | 核心问题 |
|------|------|------|----------|
| **核心链构建** | 20.64s | 78.1% | 大量重复调用 `find_nearest_rtk_distance()` |
| ├─ RTK最近点查询 | 20.51s | 77.7% | 每个感知点遍历全部3000+个RTK点 |
| └─ haversine距离计算 | 7.86s | 29.8% | 被调用126万次 |
| 预处理阶段 | 4.12s | 15.6% | CSV解析、时间转换（暂不优化） |
| 空间过滤 | 0.65s | 2.5% | 性能已足够 |

### 问题根因

1. **O(N×M) 复杂度**: 每个感知点都要遍历所有RTK点寻找最近距离
2. **重复计算**: 相同的评分计算在核心链、竞争段分析中多次执行
3. **无效计算**: 大量低质量轨迹段参与完整流程，浪费计算资源

---

## 🎯 优化目标

- [x] **主要目标**: 将核心链构建从20.64s压缩到<2s
- [x] **次要目标**: 减少重复评分计算，提升整体效率
- [x] **约束条件**: 保持算法精度，确保结果可比性

---

## 🚀 优化方案

### 方案A: 时间对齐最近点查询

#### 问题描述
当前 `find_nearest_rtk_distance()` 对每个感知点遍历全部RTK点，复杂度O(M)，总体O(N×M)。

#### 解决思路
改为基于时间的二分查找，只计算时间最近的RTK点距离。

#### 技术实现

**1. 数据预处理**
```python
# 启动时构建有序数组
rtk_times = np.array([p.timestamp.timestamp() for p in rtk_points])
rtk_lats = np.radians(np.array([p.lat for p in rtk_points]))
rtk_lons = np.radians(np.array([p.lon for p in rtk_points]))
```

**2. 查询算法**
```python
def find_nearest_rtk_distance_optimized(perception_point):
    ts = perception_point.timestamp.timestamp()
    
    # 二分查找最近时间点
    idx = np.searchsorted(rtk_times, ts)
    
    # 边界处理
    if idx == 0:
        j = 0
    elif idx == len(rtk_times):
        j = len(rtk_times) - 1
    else:
        # 比较前后两点，选择时间更近的
        if ts - rtk_times[idx-1] <= rtk_times[idx] - ts:
            j = idx - 1
        else:
            j = idx
    
    # 时间差检查
    time_diff = abs(ts - rtk_times[j])
    if time_diff > rtk_max_time_diff:
        return float('inf')  # 超出阈值，视为无匹配
    
    # 计算距离
    return haversine_distance(
        perception_point.lat, perception_point.lon,
        np.degrees(rtk_lats[j]), np.degrees(rtk_lons[j])
    )
```

**3. 配置参数**
```json
{
  "performance": {
    "rtk_max_time_diff": 3.0,
    "enable_time_aligned_search": true
  }
}
```

#### 预期收益
- **复杂度**: O(N×M) → O(N×log M)
- **耗时**: 20.51s → <1s
- **提速倍数**: >20×

---

### 方案B: 评分缓存与预裁剪

#### 问题描述
1. `unified_scoring()` 在核心链、竞争段分析中重复计算
2. 大量低质量轨迹段参与完整流程，浪费资源

#### 解决思路
在走廊过滤后立即进行完整评分，缓存结果并提前淘汰低分段。

#### 技术实现

**1. 新流程设计**
```mermaid
graph TD
    A[走廊过滤] --> B[构建轨迹段]
    B --> C[完整版评分]
    C --> D[缓存评分结果]
    D --> E{分数 >= 阈值?}
    E -->|是| F[进入核心链]
    E -->|否| G[提前淘汰]
    F --> H[核心链分析<br/>使用缓存分数]
```

**2. 评分缓存系统**
```python
class ScoreCache:
    def __init__(self):
        self._cache = {}
        self._stats = {'hits': 0, 'misses': 0}
    
    def get_score(self, segment_id, segment, scorer_func):
        if segment_id in self._cache:
            self._stats['hits'] += 1
            return self._cache[segment_id]
        
        self._stats['misses'] += 1
        score = scorer_func(segment)
        self._cache[segment_id] = score
        return score
    
    def clear(self):
        self._cache.clear()
        self._stats = {'hits': 0, 'misses': 0}
```

**3. 预裁剪逻辑**
```python
def pre_filter_segments(segments, score_cache, threshold):
    """走廊过滤后的预裁剪"""
    kept_segments = []
    discarded_segments = []
    
    for segment in segments:
        score = score_cache.get_score(
            segment.id, segment, unified_scoring
        )
        
        if score >= threshold:
            kept_segments.append(segment)
        else:
            discarded_segments.append((segment, score))
    
    logger.info(f"预裁剪完成: 保留{len(kept_segments)}, "
                f"淘汰{len(discarded_segments)}")
    
    return kept_segments, discarded_segments
```

**4. 配置参数**
```json
{
  "scoring": {
    "pre_filter_threshold": 0.4,
    "enable_score_cache": true,
    "cache_debug_output": true
  }
}
```

#### 预期收益
- **重复计算**: 完全消除
- **计算量减少**: 50-70%（取决于低分段比例）
- **缓存命中率**: >90%

---

## 📝 实施计划

### Phase 1: 时间对齐查询优化
**预计用时**: 1天  
**优先级**: 高

**任务清单**:
- [ ] 实现 `find_nearest_rtk_distance_optimized()`
- [ ] 添加配置参数 `rtk_max_time_diff`
- [ ] 在 `simple_distance_matcher.py` 中集成
- [ ] 添加性能对比日志
- [ ] 单元测试验证精度

### Phase 2: 评分缓存系统
**预计用时**: 1天  
**优先级**: 高

**任务清单**:
- [ ] 实现 `ScoreCache` 类
- [ ] 修改 `build_segments()` 后的流程
- [ ] 实现预裁剪逻辑
- [ ] 更新核心链构建使用缓存
- [ ] 添加调试输出和统计

### Phase 3: 测试与调优
**预计用时**: 0.5天  
**优先级**: 中

**任务清单**:
- [ ] 端到端性能测试
- [ ] 精度对比验证
- [ ] 参数调优（`pre_filter_threshold`等）
- [ ] 文档更新


---

## ⚠️ 风险评估与对策

### 风险1: 时间对齐精度损失
**风险等级**: 中  
**影响**: 可能影响匹配精度  
**对策**: 
- 设置合理的 `rtk_max_time_diff` 阈值（建议2-3秒）
- 提供配置开关，可回退到原算法
- 在测试阶段详细对比精度差异

### 风险2: 预裁剪误杀优质轨迹
**风险等级**: 中  
**影响**: 可能丢失有价值的轨迹段  
**对策**:
- 初期设置较低阈值（0.3-0.4）
- 输出被淘汰段的详细信息用于人工检查
- 提供详细的评分分布统计

### 风险3: 缓存一致性问题
**风险等级**: 低  
**影响**: 轨迹段修改后缓存失效  
**对策**:
- 在轨迹段分裂/合并时清理相关缓存
- 添加缓存版本控制机制

---

## 📈 预期效果

### 性能提升
| 阶段 | 当前耗时 | 优化后耗时 | 提升倍数 |
|------|----------|------------|----------|
| Phase 1完成 | 26s | ~6s | 4.3× |
| Phase 2完成 | 6s | ~3s | 8.7× |
| Phase 3完成 | 3s | ~2s | 13× |

### 资源消耗
- **内存增加**: <50MB（主要是RTK数组和评分缓存）
- **开发复杂度**: 中等
- **维护成本**: 低

---

## 🔧 配置参考

### 新增配置项
```json
{
  "performance": {
    "rtk_max_time_diff": 3.0,
    "enable_time_aligned_search": true
  },
  "scoring": {
    "pre_filter_threshold": 0.4,
    "enable_score_cache": true,
    "cache_debug_output": false
  }
}
```

### 兼容性设置
```json
{
  "compatibility": {
    "use_legacy_rtk_search": false,
    "disable_pre_filtering": false
  }
}
```

---

## 📚 相关文档

- [配置参数说明](config_reference.md)
- [性能测试报告](performance_test_results.md)
- [算法精度对比](accuracy_comparison.md)

---

## 📞 联系信息

如有问题或建议，请联系开发团队或在项目仓库提交Issue。

---

*最后更新: 2025-01-30*
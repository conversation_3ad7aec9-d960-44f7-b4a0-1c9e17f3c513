#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出RTK和感知数据为CSV格式，用于地图可视化
"""

import sys
import pandas as pd
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

sys.path.append('.')
from core.preprocessor import RawDataPreprocessor

def export_rtk_data(file_path: str, output_path: str):
    """导出RTK数据为CSV"""
    logger.info(f"处理RTK文件: {file_path}")
    
    preprocessor = RawDataPreprocessor()
    rtk_data = preprocessor.process_nmea_data(file_path)
    
    if not rtk_data:
        logger.warning("RTK数据为空")
        return
    
    # 转换为DataFrame
    df = pd.DataFrame(rtk_data)
    
    # 添加数据类型标识
    df['data_type'] = 'RTK'
    
    # 重新排列列顺序
    columns = ['timestamp', 'lat', 'lon', 'altitude', 'speed', 'heading', 'data_type']
    df = df[columns]
    
    # 保存CSV
    df.to_csv(output_path, index=False, encoding='utf-8-sig')
    logger.info(f"RTK数据已保存到: {output_path}")
    logger.info(f"RTK数据统计: {len(df)}条记录")
    
    # 打印位置范围
    print(f"\nRTK数据范围:")
    print(f"  纬度: {df['lat'].min():.6f} ~ {df['lat'].max():.6f}")
    print(f"  经度: {df['lon'].min():.6f} ~ {df['lon'].max():.6f}")
    print(f"  时间: {df['timestamp'].min()} ~ {df['timestamp'].max()}")
    
    return df

def export_perception_data(file_path: str, output_path: str):
    """导出感知数据为CSV"""
    logger.info(f"处理感知文件: {file_path}")
    
    preprocessor = RawDataPreprocessor()
    perception_data = preprocessor.process_json_data(file_path)
    
    if not perception_data:
        logger.warning("感知数据为空")
        return
    
    # 转换为DataFrame
    df = pd.DataFrame(perception_data)
    
    # 添加数据类型标识
    df['data_type'] = 'Perception'
    
    # 重新排列列顺序，包含更多感知数据字段
    columns = ['timestamp', 'lat', 'lon', 'altitude', 'speed', 'heading', 
               'id', 'vehicle_class', 'ptc_type', 'vehicle_height', 
               'vehicle_width', 'vehicle_length', 'data_type']
    df = df[columns]
    
    # 保存CSV
    df.to_csv(output_path, index=False, encoding='utf-8-sig')
    logger.info(f"感知数据已保存到: {output_path}")
    logger.info(f"感知数据统计: {len(df)}条记录")
    
    # 打印位置范围
    print(f"\n感知数据范围:")
    print(f"  纬度: {df['lat'].min():.6f} ~ {df['lat'].max():.6f}")
    print(f"  经度: {df['lon'].min():.6f} ~ {df['lon'].max():.6f}")
    print(f"  时间: {df['timestamp'].min()} ~ {df['timestamp'].max()}")
    print(f"  对象ID数量: {df['id'].nunique()}")
    print(f"  主要对象ID: {df['id'].value_counts().head().to_dict()}")
    
    return df

def export_combined_data(rtk_df, perception_df, output_path: str):
    """导出合并的数据"""
    if rtk_df is None or perception_df is None:
        logger.warning("无法合并数据，某个数据集为空")
        return
    
    # 确保列一致
    rtk_columns = ['timestamp', 'lat', 'lon', 'altitude', 'speed', 'heading', 'data_type']
    perception_columns = ['timestamp', 'lat', 'lon', 'altitude', 'speed', 'heading', 'data_type']
    
    rtk_subset = rtk_df[rtk_columns].copy()
    perception_subset = perception_df[perception_columns].copy()
    
    # 合并数据
    combined_df = pd.concat([rtk_subset, perception_subset], ignore_index=True)
    
    # 按时间排序
    combined_df['timestamp_dt'] = pd.to_datetime(combined_df['timestamp'])
    combined_df = combined_df.sort_values('timestamp_dt')
    combined_df = combined_df.drop('timestamp_dt', axis=1)
    
    # 保存CSV
    combined_df.to_csv(output_path, index=False, encoding='utf-8-sig')
    logger.info(f"合并数据已保存到: {output_path}")
    logger.info(f"合并数据统计: {len(combined_df)}条记录 (RTK: {len(rtk_subset)}, 感知: {len(perception_subset)})")
    
    return combined_df

def main():
    """主函数"""
    print("=" * 60)
    print("RTK和感知数据CSV导出工具")
    print("=" * 60)
    
    # 文件路径
    rtk_file = './data/31.log'
    perception_file = './data/save_1753355915725.txt'
    
    # 输出路径
    rtk_csv = 'output/rtk_data.csv'
    perception_csv = 'output/perception_data.csv'
    combined_csv = 'output/combined_data.csv'
    
    # 确保输出目录存在
    import os
    os.makedirs('output', exist_ok=True)
    
    try:
        # 导出RTK数据
        rtk_df = export_rtk_data(rtk_file, rtk_csv)
        
        # 导出感知数据
        perception_df = export_perception_data(perception_file, perception_csv)
        
        # 导出合并数据
        combined_df = export_combined_data(rtk_df, perception_df, combined_csv)
        
        print(f"\n✅ 数据导出完成！")
        print(f"📁 RTK数据: {rtk_csv}")
        print(f"📁 感知数据: {perception_csv}")
        print(f"📁 合并数据: {combined_csv}")
        
        # 计算距离差异
        if rtk_df is not None and perception_df is not None:
            rtk_center_lat = rtk_df['lat'].mean()
            rtk_center_lon = rtk_df['lon'].mean()
            perception_center_lat = perception_df['lat'].mean()
            perception_center_lon = perception_df['lon'].mean()
            
            lat_diff = abs(rtk_center_lat - perception_center_lat)
            lon_diff = abs(rtk_center_lon - perception_center_lon)
            
            # 简单距离估算
            lat_distance_km = lat_diff * 111
            lon_distance_km = lon_diff * 96
            total_distance_km = (lat_distance_km**2 + lon_distance_km**2)**0.5
            
            print(f"\n📍 位置分析:")
            print(f"  RTK中心: ({rtk_center_lat:.6f}, {rtk_center_lon:.6f})")
            print(f"  感知中心: ({perception_center_lat:.6f}, {perception_center_lon:.6f})")
            print(f"  中心距离: 约{total_distance_km:.1f}km")
        
    except Exception as e:
        logger.error(f"导出失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

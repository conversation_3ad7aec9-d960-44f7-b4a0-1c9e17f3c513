# 航向角处理指南

## 📖 概述

本文档详细说明车路协同轨迹匹配系统中航向角数据的定义、处理方法和精度误差计算。

## 🧭 航向角定义统一

### ✅ 定义一致性确认

经过详细分析，**RTK数据和感知数据的航向角定义完全一致**：

| 数据类型 | 定义标准 | 零度方向 | 旋转方向 | 角度范围 |
|---------|---------|---------|---------|---------|
| RTK数据 | GPS COG | 正北 (0°) | 顺时针 | 0-360° |
| 感知数据 | PtcHeading | 正北 (0°) | 顺时针 | 0-360° |

### 数据来源详解

#### 1. RTK数据航向角

**数据来源**: NMEA格式GPS数据
- **NMEA语句**: `$GPRMC` 第8字段 (course)
- **标准定义**: Course Over Ground (COG) - 地面航向角
- **物理含义**: 车辆相对于地面的实际运动方向

<augment_code_snippet path="core/preprocessor.py" mode="EXCERPT">
```python
# RTK数据处理 - 第151行
course = parts[8]  # GPRMC语句中的航向角字段
'heading': float(course) if course else 0.0
```
</augment_code_snippet>

**示例数据**:
```
$GPRMC,020425.50,D,3027.82211807,N,11407.47686488,E,18.496,241.4,250225,4.7,W,R,S
                                                           ^^^^^ 航向角: 241.4°
```

#### 2. 感知数据航向角

**数据来源**: JSON格式感知数据
- **字段名称**: `PtcHeading`
- **定义**: 目标物体的朝向角度
- **物理含义**: 感知系统检测到的目标朝向

<augment_code_snippet path="core/preprocessor.py" mode="EXCERPT">
```python
# 感知数据处理 - 第214行
'heading': float(obj.get('PtcHeading', 0.0))
```
</augment_code_snippet>

**示例数据**:
```json
{
  "ID": "10069",
  "PtcHeading": 151.17453002929688,  // 航向角: 151.17°
  "PtcLat": 30.461870595755812,
  "PtcLon": 114.12340088237073
}
```

## 📊 航向角误差计算

### 计算方法

航向角误差计算需要特别处理360°环绕问题：

<augment_code_snippet path="core/anomaly_analysis/accuracy_analyzer.py" mode="EXCERPT">
```python
def _calculate_heading_error(self, rtk_heading: float, perception_heading: float) -> float:
    # 标准化到[0, 360)范围
    h1 = rtk_heading % 360
    h2 = perception_heading % 360
    
    # 计算最小角度差
    diff = abs(h1 - h2)
    return min(diff, 360 - diff)
```
</augment_code_snippet>

### 误差计算示例

| RTK航向 | 感知航向 | 直接差值 | 环绕处理 | 最终误差 |
|---------|---------|---------|---------|---------|
| 10° | 20° | 10° | 10° | **10°** |
| 350° | 10° | 340° | 20° | **20°** |
| 10° | 350° | 340° | 20° | **20°** |
| 0° | 180° | 180° | 180° | **180°** |
| 90° | 270° | 180° | 180° | **180°** |

### 实际测试结果

从最新的精度分析结果可以看到：

<augment_code_snippet path="output/results/rtk_part005_AJ06993PAJ00115B1_diagnostic.json" mode="EXCERPT">
```json
"heading_accuracy": {
  "count": 173,
  "mean": 2.25,        // 平均航向角误差: 2.25°
  "std": 3.777,        // 标准差: 3.78°
  "min": 0.0,          // 最小误差: 0°
  "max": 14.44,        // 最大误差: 14.44°
  "p50": 0.5,          // 中位数: 0.5°
  "p95": 12.546        // 95分位数: 12.55°
}
```
</augment_code_snippet>

## 🔄 航向角插值处理

### 插值算法

当RTK和感知数据时间戳不对齐时，需要对RTK航向角进行插值：

<augment_code_snippet path="core/anomaly_analysis/accuracy_analyzer.py" mode="EXCERPT">
```python
def _interpolate_heading(self, heading_before: float, heading_after: float, ratio: float) -> float:
    # 标准化到[0, 360)范围
    h1 = heading_before % 360
    h2 = heading_after % 360
    
    # 计算角度差，处理跨越0°的情况
    diff = h2 - h1
    if diff > 180:
        diff -= 360
    elif diff < -180:
        diff += 360
    
    # 线性插值
    interpolated = h1 + diff * ratio
    return interpolated % 360
```
</augment_code_snippet>

### 插值示例

| 前航向 | 后航向 | 插值比例 | 计算过程 | 结果 |
|--------|--------|----------|----------|------|
| 10° | 20° | 0.5 | 10 + (20-10) × 0.5 | **15°** |
| 350° | 10° | 0.5 | 350 + (10-350+360) × 0.5 | **0°** |
| 10° | 350° | 0.5 | 10 + (350-10-360) × 0.5 | **0°** |

## 🎯 坐标系统说明

### GPS坐标系统 (RTK数据)

- **参考系**: WGS84地理坐标系
- **北向定义**: 地理北极方向
- **航向角**: Course Over Ground (COG)
- **特点**: 基于GPS卫星定位的真实运动方向

### 感知坐标系统 (感知数据)

- **参考系**: 与RTK一致的地理坐标系
- **北向定义**: 地理北极方向
- **航向角**: 目标物体朝向
- **特点**: 基于视觉/雷达感知的目标朝向

### 坐标系一致性

✅ **确认**: 两个系统使用相同的坐标系定义
- 都以地理北极为0°参考
- 都使用顺时针方向为正
- 都使用0-360°角度范围

## 📈 精度分析结果解读

### 航向角误差分布

根据实际测试数据：

| 误差范围 | 数据占比 | 说明 |
|---------|---------|------|
| 0-1° | ~50% | 高精度匹配 |
| 1-5° | ~25% | 良好匹配 |
| 5-15° | ~20% | 可接受误差 |
| >15° | ~5% | 需要关注的大误差 |

### 误差来源分析

1. **时间同步误差**: RTK和感知数据的时间戳不完全对齐
2. **感知精度限制**: 视觉/雷达感知的固有精度限制
3. **运动状态影响**: 车辆转弯、变道时的动态误差
4. **环境因素**: GPS信号质量、感知环境复杂度

## 🔧 配置参数

### 航向角相关配置

```json
{
  "accuracy_analysis": {
    "heading_error_thresholds": [5.0, 10.0, 30.0],  // 航向角误差阈值(度)
    "interpolation_method": "linear",                 // 插值方法
    "max_time_gap": 2.0                              // 最大时间间隔(秒)
  }
}
```

### 方向一致性检查

```json
{
  "corridor": {
    "direction_threshold_degrees": 60.0  // 方向阈值(度)
  }
}
```

## 🚨 注意事项

### 1. 角度环绕处理
- 所有航向角计算都考虑了360°环绕问题
- 误差计算使用最小角度差
- 插值处理跨越0°的情况

### 2. 数据质量影响
- GPS信号质量影响RTK航向角精度
- 感知算法精度影响PtcHeading准确性
- 低速或静止状态下航向角可能不稳定

### 3. 时间同步重要性
- 航向角对时间同步较为敏感
- 建议时间同步误差控制在1秒以内
- 插值仅在合理时间间隔内进行

## 📊 输出格式

### CSV输出
匹配结果CSV中的航向角相关字段：
- `rtk_heading`: RTK航向角 (度)
- `per_heading`: 感知航向角 (度)
- `heading_error_deg`: 航向角误差 (度)

### JSON诊断报告
精度分析JSON中的航向角统计：
```json
{
  "heading_accuracy": {
    "mean": 2.25,      // 平均误差(度)
    "std": 3.777,      // 标准差(度)
    "p50": 0.5,        // 中位数(度)
    "p95": 12.546,     // 95分位数(度)
    "unit": "degrees"  // 单位
  }
}
```

## 🔍 故障排除

### 问题1: 航向角误差异常大
**可能原因**:
- 坐标系定义不一致
- 时间同步问题
- 感知算法精度问题

**解决方法**:
1. 确认两个系统都使用地理北极为参考
2. 检查时间戳对齐
3. 分析感知数据质量

### 问题2: 插值结果异常
**可能原因**:
- 跨越0°处理错误
- 时间间隔过大

**解决方法**:
- 检查插值算法的360°环绕处理
- 限制插值的最大时间间隔

### 问题3: 静止状态航向角不稳定
**说明**: 这是正常现象
- GPS在静止状态下航向角精度较低
- 感知系统在静止目标上航向角可能不准确
- 建议在分析时过滤低速状态数据

## 📚 相关文档

- [速度单位处理指南](speed_unit_guide.md) - 速度数据处理
- [配置参考手册](config_reference.md) - 精度分析配置
- [使用示例](gap_analysis_usage_example.md) - 实际使用案例

---

**重要结论**: RTK和感知数据的航向角定义完全一致，都使用正北为0°、顺时针方向的标准定义，系统正确处理了360°环绕和插值问题，确保了航向角误差计算的准确性。

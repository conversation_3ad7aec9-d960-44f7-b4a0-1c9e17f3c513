#!/usr/bin/env python3
"""
高度优化方法的实际使用示例
展示如何在轨迹匹配中集成优化的RTK距离查询
"""

import numpy as np
from datetime import datetime, timedelta
from core.data_utils import RTKPoint, PerceptionPoint, Config
from core.simple_distance_matcher import UnifiedTrajectoryScorer

class OptimizedTrajectoryMatcher:
    """集成了优化RTK查询的轨迹匹配器"""
    
    def __init__(self, config):
        self.config = config
        self.scorer = UnifiedTrajectoryScorer(config)
        
        # 预处理数据存储
        self.rtk_points_sorted = None
        self.rtk_times_array = None
        self.is_preprocessed = False
    
    def preprocess_rtk_data(self, rtk_points):
        """
        预处理RTK数据：排序并构建时间索引
        这个步骤只需要在程序启动时执行一次
        """
        print(f"预处理 {len(rtk_points)} 个RTK点...")
        
        # 按时间排序
        self.rtk_points_sorted = sorted(rtk_points, key=lambda p: p.timestamp.timestamp())
        
        # 构建时间戳数组（用于二分查找）
        self.rtk_times_array = np.array([p.timestamp.timestamp() for p in self.rtk_points_sorted])
        
        self.is_preprocessed = True
        print(f"预处理完成！时间范围：{self.rtk_points_sorted[0].timestamp} 到 {self.rtk_points_sorted[-1].timestamp}")
    
    def calculate_segment_quality_optimized(self, segment, time_window=3.0):
        """
        使用优化方法计算轨迹段质量
        """
        if not self.is_preprocessed:
            raise ValueError("请先调用 preprocess_rtk_data() 进行预处理")
        
        if not hasattr(segment, 'points') or not segment.points:
            return 0.0
        
        # 使用优化方法计算每个点的距离
        distances = []
        for point in segment.points:
            distance = self.scorer.find_nearest_rtk_distance_optimized(
                point, self.rtk_points_sorted, self.rtk_times_array, time_window
            )
            distances.append(distance)
        
        # 转换为评分
        scores = [self.scorer.distance_to_score(d) for d in distances]
        return np.mean(scores)
    
    def batch_calculate_distances(self, perception_points, time_window=3.0):
        """
        批量计算感知点到RTK的最近距离
        展示优化方法在批量处理中的优势
        """
        if not self.is_preprocessed:
            raise ValueError("请先调用 preprocess_rtk_data() 进行预处理")
        
        print(f"批量计算 {len(perception_points)} 个感知点的RTK距离...")
        
        distances = []
        for i, point in enumerate(perception_points):
            distance = self.scorer.find_nearest_rtk_distance_optimized(
                point, self.rtk_points_sorted, self.rtk_times_array, time_window
            )
            distances.append(distance)
            
            if (i + 1) % 100 == 0:
                print(f"已处理 {i + 1}/{len(perception_points)} 个点")
        
        return distances

def demo_optimized_usage():
    """演示优化方法的使用"""
    
    # 1. 创建配置和匹配器
    config = Config()
    matcher = OptimizedTrajectoryMatcher(config)
    
    # 2. 生成示例数据
    print("=== 生成示例数据 ===")
    base_time = datetime.now()
    base_lat, base_lon = 39.9042, 116.4074
    
    # RTK数据
    rtk_points = []
    for i in range(5000):
        timestamp = base_time + timedelta(milliseconds=i * 100)
        lat = base_lat + i * 0.00001
        lon = base_lon + i * 0.00001
        rtk_points.append(RTKPoint(timestamp, lat, lon, 15.0, 45.0))
    
    # 感知数据
    perception_points = []
    for i in range(200):
        time_offset = np.random.uniform(0, 500)
        timestamp = base_time + timedelta(seconds=time_offset)
        progress = time_offset / 500.0
        lat = base_lat + progress * 5000 * 0.00001 + np.random.normal(0, 0.00005)
        lon = base_lon + progress * 5000 * 0.00001 + np.random.normal(0, 0.00005)
        perception_points.append(PerceptionPoint(timestamp, f"obj_{i}", lat, lon, 15.0, 45.0))
    
    # 3. 预处理RTK数据（关键步骤！）
    print("\n=== 预处理RTK数据 ===")
    matcher.preprocess_rtk_data(rtk_points)
    
    # 4. 批量计算距离
    print("\n=== 批量计算距离 ===")
    import time
    start_time = time.time()
    distances = matcher.batch_calculate_distances(perception_points)
    end_time = time.time()
    
    print(f"批量计算完成！")
    print(f"耗时: {end_time - start_time:.4f}s")
    print(f"平均距离: {np.mean(distances):.2f}m")
    print(f"距离范围: {np.min(distances):.2f}m - {np.max(distances):.2f}m")
    
    # 5. 展示关键统计信息
    print(f"\n=== 性能统计 ===")
    print(f"RTK点数: {len(rtk_points):,}")
    print(f"感知点数: {len(perception_points):,}")
    print(f"总比较次数（原始方法）: {len(rtk_points) * len(perception_points):,}")
    print(f"平均每点耗时: {(end_time - start_time) / len(perception_points) * 1000:.2f}ms")

if __name__ == "__main__":
    demo_optimized_usage()

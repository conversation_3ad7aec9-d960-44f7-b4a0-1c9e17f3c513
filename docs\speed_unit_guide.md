# 速度单位处理指南

## 📖 概述

本文档详细说明车路协同轨迹匹配系统中速度数据的单位处理、转换逻辑和精度误差计算方法。

## 🎯 速度单位统一

### 统一目标
所有速度数据最终统一为 **米/秒 (m/s)** 单位，确保RTK和感知数据的速度误差计算准确性。

### 数据来源与转换

#### 1. RTK数据速度处理

**数据来源**: NMEA格式的GPS数据
- **原始格式**: `$GPRMC` 语句中的速度字段
- **原始单位**: **节 (knots)**
- **转换公式**: `speed_ms = speed_knots × 0.514444`
- **最终单位**: **米/秒 (m/s)**

**代码实现**:
```python
# core/preprocessor.py 第158行
speed_knots = parts[7]  # GPRMC语句中的速度字段
speed_ms = float(speed_knots) * 0.514444 if speed_knots else 0.0
```

**示例转换**:
- 10 knots → 5.14 m/s → 18.5 km/h
- 4.817 knots → 2.48 m/s → 8.93 km/h

#### 2. 感知数据速度处理

**数据来源**: JSON格式的感知数据
- **原始格式**: `PtcSpeed` 字段
- **原始单位**: **公里/小时 (km/h)**
- **转换公式**: `speed_ms = speed_kmh ÷ 3.6`
- **最终单位**: **米/秒 (m/s)**

**代码实现**:
```python
# core/preprocessor.py 第205-206行
speed_kmh = float(obj.get('PtcSpeed', 0.0))
speed_ms = speed_kmh / 3.6  # km/h 转换为 m/s
```

**示例转换**:
- 18.5 km/h → 5.14 m/s
- 31.91 km/h → 8.86 m/s
- 0.283 km/h → 0.079 m/s

## 📊 速度精度误差计算

### 计算流程

#### 1. 时间对齐与插值
当感知数据时间戳与RTK数据不完全对齐时，使用线性插值：

```python
# core/anomaly_analysis/accuracy_analyzer.py 第339行
speed_interpolated = rtk_before.speed + (rtk_after.speed - rtk_before.speed) * ratio
```

#### 2. 误差计算公式

**绝对误差**:
```python
speed_abs_error = abs(rtk_interpolated['speed'] - perception['speed'])
```
- **单位**: 米/秒 (m/s)
- **含义**: RTK速度与感知速度的绝对差值

**相对误差**:
```python
speed_rel_error = (speed_abs_error / max(rtk_interpolated['speed'], 0.1)) * 100
```
- **单位**: 百分比 (%)
- **含义**: 相对于RTK速度的误差百分比
- **注意**: 使用 `max(rtk_speed, 0.1)` 避免除零错误

### 误差分析示例

假设某时刻的数据：
- RTK速度: 5.14 m/s (转换自10 knots)
- 感知速度: 5.42 m/s (转换自19.5 km/h)

计算结果：
- 绝对误差: |5.14 - 5.42| = 0.28 m/s
- 相对误差: (0.28 / 5.14) × 100 = 5.45%

## 🔧 单位转换对照表

| 原始单位 | 转换系数 | 目标单位 | 示例 |
|---------|---------|---------|------|
| 节 (knots) | × 0.514444 | m/s | 10 knots = 5.14 m/s |
| km/h | ÷ 3.6 | m/s | 18.5 km/h = 5.14 m/s |
| m/s | × 3.6 | km/h | 5.14 m/s = 18.5 km/h |
| m/s | ÷ 0.514444 | knots | 5.14 m/s = 10 knots |

## 📈 数据验证

### 速度合理性检查

系统会对速度数据进行验证：

```python
# core/data_utils.py 第514行
if (df['speed'] < 0).any():
    errors.append("速度值不能为负")
```

### 常见速度范围

| 场景 | 速度范围 (m/s) | 速度范围 (km/h) |
|------|---------------|----------------|
| 静止车辆 | 0 - 0.5 | 0 - 1.8 |
| 城市道路 | 0.5 - 16.7 | 1.8 - 60 |
| 高速公路 | 16.7 - 33.3 | 60 - 120 |

## 🚨 注意事项

### 1. 精度影响因素
- **GPS精度**: RTK数据的速度精度受GPS信号质量影响
- **感知算法**: 感知数据的速度精度受目标检测和跟踪算法影响
- **时间同步**: 时间戳不对齐会影响速度比较的准确性

### 2. 异常值处理
- **零速度**: 当RTK速度接近0时，使用0.1作为最小值避免相对误差计算异常
- **负速度**: 系统会检测并报告负速度值作为数据异常
- **超高速度**: 超出合理范围的速度值会被标记为可疑数据

### 3. 插值注意事项
- **时间间隔**: 插值仅在合理的时间间隔内进行（默认≤2秒）
- **运动连续性**: 插值假设车辆运动连续，不适用于急停急起场景
- **边界处理**: 轨迹起始和结束点附近的插值需要特别注意

## 📊 输出格式

### CSV输出
匹配结果CSV中的速度相关字段：
- `rtk_speed`: RTK速度 (m/s)
- `perception_speed`: 感知速度 (m/s)
- `speed_error_abs`: 速度绝对误差 (m/s)
- `speed_error_rel`: 速度相对误差 (%)

### JSON诊断报告
精度分析JSON中的速度统计：
```json
{
  "speed_accuracy": {
    "mean_abs_error": 0.28,
    "std_abs_error": 0.15,
    "mean_rel_error": 5.45,
    "p95_abs_error": 0.52,
    "unit": "m/s for absolute, % for relative"
  }
}
```

## 🔍 故障排除

### 问题1: 速度误差异常大
**可能原因**:
- 单位转换错误
- 时间同步问题
- 数据质量问题

**解决方法**:
1. 检查原始数据的速度单位
2. 验证时间戳对齐
3. 查看数据预处理日志

### 问题2: 相对误差计算异常
**可能原因**:
- RTK速度为0或接近0
- 除零错误

**解决方法**:
- 系统已使用 `max(rtk_speed, 0.1)` 处理
- 检查RTK数据质量

### 问题3: 速度转换验证
**验证方法**:
```python
# 验证转换是否正确
original_kmh = 18.5
converted_ms = original_kmh / 3.6
back_to_kmh = converted_ms * 3.6
print(f"原始: {original_kmh} km/h")
print(f"转换: {converted_ms:.3f} m/s") 
print(f"反算: {back_to_kmh:.3f} km/h")
```

## 📚 相关文档

- [配置参考手册](config_reference.md) - 精度分析配置
- [使用示例](gap_analysis_usage_example.md) - 实际使用案例
- [评分系统指南](scoring_system_guide.md) - 轨迹匹配评分

---

**重要提醒**: 所有速度相关的分析和比较都基于统一的m/s单位，确保了系统的一致性和准确性。

#!/usr/bin/env python3
"""
RTK距离查询优化测试脚本
测试不同优化方法的性能和准确性
"""

import sys
import os
import logging
from datetime import datetime, timedelta
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.data_utils import RTKPoint, PerceptionPoint, Config
from core.simple_distance_matcher import UnifiedTrajectoryScorer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def generate_test_data():
    """生成测试数据"""
    logger.info("生成测试数据...")
    
    # 基准时间和位置
    base_time = datetime.now()
    base_lat = 39.9042
    base_lon = 116.4074
    
    # 生成RTK轨迹点（模拟10000个点，间隔100ms）
    rtk_points = []
    for i in range(10000):
        timestamp = base_time + timedelta(milliseconds=i * 100)
        # 模拟车辆沿直线行驶
        lat = base_lat + i * 0.00001  # 每个点向北移动约1.1米
        lon = base_lon + i * 0.00001  # 每个点向东移动约0.8米
        speed = 15.0 + np.random.normal(0, 1.0)  # 15m/s ± 1m/s
        heading = 45.0 + np.random.normal(0, 5.0)  # 45° ± 5°
        
        rtk_points.append(RTKPoint(
            timestamp=timestamp,
            lat=lat,
            lon=lon,
            speed=max(0, speed),
            heading=heading % 360
        ))
    
    # 生成感知数据点（模拟1000个点，时间分布在RTK轨迹范围内）
    perception_points = []
    for i in range(1000):
        # 随机选择时间点（在RTK轨迹时间范围内）
        time_offset = np.random.uniform(0, 1000)  # 0-1000秒范围内
        timestamp = base_time + timedelta(seconds=time_offset)

        # 基于时间插值计算大致位置，然后添加一些误差
        progress = time_offset / 1000.0
        lat = base_lat + progress * 10000 * 0.00001 + np.random.normal(0, 0.00005)
        lon = base_lon + progress * 10000 * 0.00001 + np.random.normal(0, 0.00005)
        speed = 15.0 + np.random.normal(0, 2.0)
        heading = 45.0 + np.random.normal(0, 10.0)
        
        perception_points.append(PerceptionPoint(
            timestamp=timestamp,
            id=f"obj_{i}",
            lat=lat,
            lon=lon,
            speed=max(0, speed),
            heading=heading % 360
        ))
    
    logger.info(f"生成了 {len(rtk_points)} 个RTK点和 {len(perception_points)} 个感知点")
    return rtk_points, perception_points

def test_optimization():
    """测试优化效果"""
    logger.info("=== RTK距离查询优化测试 ===")
    
    # 生成测试数据
    rtk_points, perception_points = generate_test_data()
    
    # 创建配置和评分器
    config = Config()
    scorer = UnifiedTrajectoryScorer(config)
    
    # 运行性能基准测试
    results = scorer.benchmark_rtk_distance_methods(
        perception_points, rtk_points, time_window=3.0
    )
    
    # 分析结果
    logger.info("=== 性能分析总结 ===")
    original_time = results['original']['time']
    window_time = results['window']['time']
    optimized_time = results['optimized']['time']
    
    window_speedup = original_time / window_time
    optimized_speedup = original_time / optimized_time
    
    logger.info(f"窗口优化方法提速: {window_speedup:.2f}x")
    logger.info(f"高度优化方法提速: {optimized_speedup:.2f}x")
    
    # 预估全量数据的性能提升
    total_perception_points = len(perception_points)
    estimated_original_time = original_time * (total_perception_points / 100)
    estimated_window_time = window_time * (total_perception_points / 100)
    estimated_optimized_time = optimized_time * (total_perception_points / 100)
    
    logger.info("")
    logger.info("=== 全量数据性能预估 ===")
    logger.info(f"感知点总数: {total_perception_points}")
    logger.info(f"原始方法预估耗时: {estimated_original_time:.2f}s")
    logger.info(f"窗口优化预估耗时: {estimated_window_time:.2f}s")
    logger.info(f"高度优化预估耗时: {estimated_optimized_time:.2f}s")
    
    # 计算理论上的性能提升
    logger.info("")
    logger.info("=== 理论性能提升 ===")
    logger.info(f"原始复杂度: O(N×M) = O({total_perception_points}×{len(rtk_points)}) = {total_perception_points * len(rtk_points):,} 次比较")
    
    # 窗口优化：假设平均时间窗口内有30个RTK点
    avg_window_points = 30
    window_comparisons = total_perception_points * avg_window_points
    logger.info(f"窗口优化复杂度: O(N×W) = O({total_perception_points}×{avg_window_points}) = {window_comparisons:,} 次比较")
    
    # 高度优化：二分查找 + 窗口内搜索
    optimized_comparisons = total_perception_points * (np.log2(len(rtk_points)) + avg_window_points)
    logger.info(f"高度优化复杂度: O(N×(log M + W)) = {optimized_comparisons:,.0f} 次比较")
    
    theoretical_window_speedup = (total_perception_points * len(rtk_points)) / window_comparisons
    theoretical_optimized_speedup = (total_perception_points * len(rtk_points)) / optimized_comparisons
    
    logger.info(f"窗口优化理论提速: {theoretical_window_speedup:.1f}x")
    logger.info(f"高度优化理论提速: {theoretical_optimized_speedup:.1f}x")
    
    return results

if __name__ == "__main__":
    try:
        results = test_optimization()
        logger.info("测试完成！")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

"""
轨迹匹配模块
实现基于简单距离的轨迹匹配算法
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
import logging
from datetime import datetime, timedelta
import math
import time

try:
    from .data_utils import GeoUtils
except ImportError:
    from data_utils import GeoUtils

logger = logging.getLogger(__name__)


class UnifiedTrajectoryScorer:
    """统一轨迹评分器 - 兼顾长短轨迹的问题"""

    def __init__(self, config):
        # 从配置中获取参数，提供默认值
        self.peak_window_duration = getattr(config, 'peak_window_duration', 5.0)
        self.min_peak_threshold = getattr(config, 'min_peak_threshold', 0.7)
        self.segment_duration = getattr(config, 'segment_duration', 5.0)
        self.quality_threshold_high = getattr(config, 'quality_threshold_high', 0.8)
        self.quality_threshold_medium = getattr(config, 'quality_threshold_medium', 0.6)
        self.short_trajectory_threshold = getattr(config, 'short_trajectory_threshold', 10.0)
        self.sampling_rate = getattr(config, 'sampling_rate', 10.0)
        self.spatial_decay_distance = getattr(config, 'spatial_decay_distance', 5.0)

        logger.info(f"统一评分器初始化: 峰值窗口={self.peak_window_duration}s, "
                   f"短轨迹阈值={self.short_trajectory_threshold}s, "
                   f"高质量阈值={self.quality_threshold_high}")

    def distance_to_score(self, distance):
        """距离转换为评分"""
        return math.exp(-distance / self.spatial_decay_distance)

    def find_nearest_rtk_distance(self, per_point, rtk_points, time_window=3.0):
        """找到感知点最近的RTK点距离"""
        min_distance = float('inf')

        # 创建GeoUtils实例
        geo_utils = GeoUtils()

        for rtk_point in rtk_points:
            time_diff = abs((per_point.timestamp - rtk_point.timestamp).total_seconds())
            if time_diff <= time_window:
                # 计算地理距离
                distance = geo_utils.haversine_distance(
                    per_point.lat, per_point.lon,
                    rtk_point.lat, rtk_point.lon
                )
                min_distance = min(min_distance, distance)

        return min_distance if min_distance != float('inf') else 10.0  # 默认10米

    def find_nearest_rtk_distance_with_window(self, per_point, rtk_points, time_window=3.0):
        """
        优化版本：使用时间窗口内的最近距离查询
        在时间窗口内找到所有候选RTK点，然后计算距离最近的点
        """
        min_distance = float('inf')
        geo_utils = GeoUtils()

        # 将时间戳转换为可比较的格式
        per_timestamp = per_point.timestamp.timestamp()

        # 在时间窗口内找到所有候选点
        candidates = []
        for rtk_point in rtk_points:
            rtk_timestamp = rtk_point.timestamp.timestamp()
            time_diff = abs(per_timestamp - rtk_timestamp)

            if time_diff <= time_window:
                candidates.append(rtk_point)

        # 如果没有候选点，返回默认值
        if not candidates:
            return 10.0

        # 在候选点中找距离最近的
        for rtk_point in candidates:
            distance = geo_utils.haversine_distance(
                per_point.lat, per_point.lon,
                rtk_point.lat, rtk_point.lon
            )
            min_distance = min(min_distance, distance)

        return min_distance if min_distance != float('inf') else 10.0

    def find_nearest_rtk_distance_optimized(self, per_point, rtk_points_sorted, rtk_times_array, time_window=3.0):
        """
        高度优化版本：使用预排序的RTK数据和二分查找
        需要预先对RTK点按时间排序，并构建时间戳数组
        """
        if not rtk_points_sorted or len(rtk_times_array) == 0:
            return 10.0

        geo_utils = GeoUtils()
        per_timestamp = per_point.timestamp.timestamp()

        # 使用二分查找找到时间窗口的边界
        start_idx = np.searchsorted(rtk_times_array, per_timestamp - time_window, side='left')
        end_idx = np.searchsorted(rtk_times_array, per_timestamp + time_window, side='right')

        # 如果没有在时间窗口内的点
        if start_idx >= end_idx:
            return 10.0

        # 在时间窗口内找距离最近的点
        min_distance = float('inf')
        for i in range(start_idx, end_idx):
            rtk_point = rtk_points_sorted[i]
            distance = geo_utils.haversine_distance(
                per_point.lat, per_point.lon,
                rtk_point.lat, rtk_point.lon
            )
            min_distance = min(min_distance, distance)

        return min_distance if min_distance != float('inf') else 10.0

    def benchmark_rtk_distance_methods(self, perception_points, rtk_points, time_window=3.0):
        """
        性能基准测试：比较不同RTK距离查询方法的性能
        """
        logger.info("开始RTK距离查询性能基准测试...")

        # 准备优化版本需要的预排序数据
        rtk_points_sorted = sorted(rtk_points, key=lambda p: p.timestamp.timestamp())
        rtk_times_array = np.array([p.timestamp.timestamp() for p in rtk_points_sorted])

        # 测试样本（取前100个感知点进行测试）
        test_points = perception_points[:min(100, len(perception_points))]

        results = {}

        # 测试原始方法
        logger.info("测试原始方法...")
        start_time = time.time()
        original_distances = []
        for point in test_points:
            distance = self.find_nearest_rtk_distance(point, rtk_points, time_window)
            original_distances.append(distance)
        original_time = time.time() - start_time
        results['original'] = {
            'time': original_time,
            'distances': original_distances,
            'avg_distance': np.mean(original_distances)
        }

        # 测试窗口优化方法
        logger.info("测试窗口优化方法...")
        start_time = time.time()
        window_distances = []
        for point in test_points:
            distance = self.find_nearest_rtk_distance_with_window(point, rtk_points, time_window)
            window_distances.append(distance)
        window_time = time.time() - start_time
        results['window'] = {
            'time': window_time,
            'distances': window_distances,
            'avg_distance': np.mean(window_distances)
        }

        # 测试高度优化方法
        logger.info("测试高度优化方法...")
        start_time = time.time()
        optimized_distances = []
        for point in test_points:
            distance = self.find_nearest_rtk_distance_optimized(
                point, rtk_points_sorted, rtk_times_array, time_window
            )
            optimized_distances.append(distance)
        optimized_time = time.time() - start_time
        results['optimized'] = {
            'time': optimized_time,
            'distances': optimized_distances,
            'avg_distance': np.mean(optimized_distances)
        }

        # 输出性能对比结果
        logger.info("=== RTK距离查询性能基准测试结果 ===")
        logger.info(f"测试样本数: {len(test_points)}")
        logger.info(f"RTK点数: {len(rtk_points)}")
        logger.info(f"时间窗口: {time_window}s")
        logger.info("")

        for method_name, result in results.items():
            logger.info(f"{method_name.upper()} 方法:")
            logger.info(f"  耗时: {result['time']:.4f}s")
            logger.info(f"  平均距离: {result['avg_distance']:.2f}m")
            if method_name != 'original':
                speedup = original_time / result['time']
                logger.info(f"  相对原始方法提速: {speedup:.2f}x")
            logger.info("")

        # 验证结果一致性
        original_arr = np.array(original_distances)
        window_arr = np.array(window_distances)
        optimized_arr = np.array(optimized_distances)

        window_diff = np.mean(np.abs(original_arr - window_arr))
        optimized_diff = np.mean(np.abs(original_arr - optimized_arr))

        logger.info("=== 结果一致性检查 ===")
        logger.info(f"窗口方法与原始方法平均差异: {window_diff:.4f}m")
        logger.info(f"优化方法与原始方法平均差异: {optimized_diff:.4f}m")

        return results

    def calculate_peak_quality(self, segment, rtk_points):
        """计算峰值质量：寻找最佳质量窗口"""
        if not hasattr(segment, 'points') or not segment.points:
            return 0.0, 0, 0

        # 计算每个点的距离误差和评分
        distances = []
        for point in segment.points:
            distance = self.find_nearest_rtk_distance(point, rtk_points)
            distances.append(distance)

        scores = [self.distance_to_score(d) for d in distances]

        window_size = int(self.peak_window_duration * self.sampling_rate)
        window_size = min(window_size, len(scores))

        if len(scores) < window_size:
            return np.mean(scores), 0, len(scores)-1

        best_score = 0
        best_start = 0
        best_end = window_size - 1

        for i in range(len(scores) - window_size + 1):
            window_score = np.mean(scores[i:i+window_size])
            if window_score > best_score:
                best_score = window_score
                best_start = i
                best_end = i + window_size - 1

        return best_score, best_start, best_end

    def calculate_segment_qualities(self, segment, rtk_points):
        """计算分段质量"""
        if not hasattr(segment, 'points') or not segment.points:
            return []

        # 计算每个点的评分
        distances = []
        for point in segment.points:
            distance = self.find_nearest_rtk_distance(point, rtk_points)
            distances.append(distance)

        scores = [self.distance_to_score(d) for d in distances]
        segment_size = int(self.segment_duration * self.sampling_rate)

        segments = []
        for i in range(0, len(scores), segment_size):
            segment_scores = scores[i:i+segment_size]
            if len(segment_scores) >= segment_size // 2:
                segment_avg = np.mean(segment_scores)
                segments.append({
                    'start_idx': i,
                    'end_idx': i + len(segment_scores) - 1,
                    'duration': len(segment_scores) / self.sampling_rate,
                    'quality': segment_avg,
                    'start_time': i / self.sampling_rate,
                    'end_time': (i + len(segment_scores) - 1) / self.sampling_rate
                })

        return segments

    def unified_scoring(self, segment, rtk_points):
        """统一评分算法"""
        if not hasattr(segment, 'points') or not segment.points:
            return 0.0, {}

        duration = segment.duration

        # 1. 基础指标计算 - 计算平均质量
        distances = []
        for point in segment.points:
            distance = self.find_nearest_rtk_distance(point, rtk_points)
            distances.append(distance)

        scores = [self.distance_to_score(d) for d in distances]
        avg_quality = np.mean(scores)

        # 2. 峰值质量计算
        peak_quality, peak_start, peak_end = self.calculate_peak_quality(segment, rtk_points)

        # 3. 分段质量计算
        segments = self.calculate_segment_qualities(segment, rtk_points)

        # 4. 高质量段统计
        high_quality_segments = [s for s in segments if s['quality'] >= self.quality_threshold_high]
        medium_quality_segments = [s for s in segments if s['quality'] >= self.quality_threshold_medium]

        # 5. 轨迹类型判断和评分策略
        if duration <= self.short_trajectory_threshold:
            # 短轨迹：主要看整体质量
            strategy = "short_trajectory"
            if avg_quality >= self.quality_threshold_high:
                final_score = avg_quality * 0.9 + (duration / self.short_trajectory_threshold) * 0.1
            else:
                final_score = avg_quality * 0.8 + (duration / self.short_trajectory_threshold) * 0.2

        elif peak_quality >= self.quality_threshold_high:
            # 长轨迹但有高质量峰值：峰值优先策略
            strategy = "peak_quality_priority"
            peak_weight = 0.7
            avg_weight = 0.2
            duration_weight = 0.1

            duration_bonus = min(duration / 30, 1.0) ** 0.5
            final_score = peak_quality * peak_weight + avg_quality * avg_weight + duration_bonus * duration_weight

        elif len(high_quality_segments) > 0:
            # 长轨迹有高质量段：分段优先策略
            strategy = "segment_quality_priority"
            best_segment_quality = max(s['quality'] for s in high_quality_segments)
            high_quality_coverage = sum(s['duration'] for s in high_quality_segments) / duration

            segment_weight = 0.6
            coverage_weight = 0.2
            avg_weight = 0.2

            final_score = (best_segment_quality * segment_weight +
                          high_quality_coverage * coverage_weight +
                          avg_quality * avg_weight)

        else:
            # 长轨迹无明显高质量段：传统策略
            strategy = "traditional_long_trajectory"
            duration_ratio = min(duration / 30, 1.0)
            final_score = avg_quality * 0.6 + duration_ratio * 0.3 + (len(segments) / 6) * 0.1

        # 6. 构建详细结果
        result = {
            'final_score': final_score,
            'strategy': strategy,
            'duration': duration,
            'avg_quality': avg_quality,
            'peak_quality': peak_quality,
            'peak_window': (peak_start / self.sampling_rate,
                           peak_end / self.sampling_rate),
            'segments': segments,
            'high_quality_segments': len(high_quality_segments),
            'medium_quality_segments': len(medium_quality_segments),
            'quality_coverage': {
                'high': sum(s['duration'] for s in high_quality_segments) / duration if duration > 0 else 0,
                'medium': sum(s['duration'] for s in medium_quality_segments) / duration if duration > 0 else 0
            }
        }

        return final_score, result


class SimpleDistanceMatcher:
    """轨迹匹配器 (支持F1-style评分)"""
    
    def __init__(self, config, rtk_points):
        self.params = config  # 保持向后兼容
        self.config = config
        self.rtk_points = rtk_points
        self.geo_utils = GeoUtils()
        
        # 初始化走廊过滤器
        try:
            from .corridor_filter import CorridorFilter
            self.corridor_filter = CorridorFilter(config)
        except ImportError as e:
            logger.warning(f"无法导入走廊过滤器: {e}")
            self.corridor_filter = None
        
        # 检查评分方法
        self.scoring_method = getattr(config, 'method', 'legacy')
        if self.scoring_method == 'f1_style':
            logger.info("使用F1-style评分方法")
        elif self.scoring_method == 'unified':
            logger.info("使用统一评分方法")
            # 初始化统一评分器
            self.unified_scorer = UnifiedTrajectoryScorer(config)
        else:
            logger.info("使用传统评分方法")
    

    
    def get_time_window_rtk(self, rtk_df: pd.DataFrame, center_time: datetime, 
                           window_sec: float) -> List[Dict]:
        """获取时间窗口内的RTK点"""
        start_time = center_time - timedelta(seconds=window_sec / 2)
        end_time = center_time + timedelta(seconds=window_sec / 2)
        
        # 过滤时间窗口内的RTK点
        mask = (rtk_df['timestamp'] >= start_time) & (rtk_df['timestamp'] <= end_time)
        windowed_rtk = rtk_df[mask]
        
        # 转换为字典列表
        rtk_points = []
        for _, row in windowed_rtk.iterrows():
            rtk_points.append({
                'timestamp': row['timestamp'],
                'lat': row['lat'],
                'lon': row['lon'],
                'speed': row['speed'],
                'heading': row['heading']
            })
        
        return rtk_points
    

    
    def calculate_local_peak(self, segment, rtk_df: pd.DataFrame) -> float:
        """计算局部峰值匹配分数 (使用简单距离)"""
        if len(segment.points) == 0:
            return 0.0
        
        # 使用简单距离评分替代DTW
        peak_score = self._calculate_peak_score(segment)
        
        logger.debug(f"轨迹段 {segment.id} 峰值匹配分数: {peak_score:.3f}")
        
        return peak_score
    
    def calculate_good_ratio(self, segment, rtk_df: pd.DataFrame) -> float:
        """计算稳定性分数（好匹配点占比）"""
        if len(segment.points) == 0:
            return 0.0
        
        # 使用简单距离评分替代DTW
        stability_score = self._calculate_stability_weight(segment)
        
        logger.debug(f"轨迹段 {segment.id} 稳定性分数: {stability_score:.3f}")
        
        return stability_score
    
    def calculate_final_score(self, segment, rtk_df: pd.DataFrame, max_duration: float) -> float:
        """计算最终综合评分"""
        if self.scoring_method == 'f1_style':
            # 使用F1-style评分
            final_score = self.calculate_f1_score(segment)
            segment.final_score = final_score
            return final_score
        elif self.scoring_method == 'unified':
            # 使用统一评分
            final_score, result = self.unified_scorer.unified_scoring(segment, self.rtk_points)
            segment.final_score = final_score

            # 保存详细评分信息到segment对象
            segment.unified_result = result
            segment.scoring_strategy = result['strategy']
            segment.avg_quality = result['avg_quality']
            segment.peak_quality = result['peak_quality']
            segment.high_quality_segments = result['high_quality_segments']

            logger.info(f"轨迹段 {segment.id} 统一评分: 策略={result['strategy']}, "
                       f"最终={final_score:.3f}, 峰值质量={result['peak_quality']:.3f}, "
                       f"平均质量={result['avg_quality']:.3f}")

            return final_score
        else:
            # 使用传统评分方式
            # 1. 峰值匹配分数
            peak_score = self.calculate_local_peak(segment, rtk_df)

            # 2. 时长优势分数
            duration_ratio = min(segment.duration / max_duration, 1.0) if max_duration > 0 else 0.0

            # 3. 稳定性分数
            stability_score = self.calculate_good_ratio(segment, rtk_df)

            # 综合评分 - 兼容旧配置
            peak_weight = getattr(self.params, 'peak_weight', 0.6)
            duration_weight = getattr(self.params, 'duration_weight', 0.3)
            stability_weight = getattr(self.params, 'stability_weight', 0.1)

            final_score = (
                peak_score * peak_weight +
                duration_ratio * duration_weight +
                stability_score * stability_weight
            )

            # 保存各项分数
            segment.peak_score = peak_score
            segment.final_score = final_score

            logger.info(f"轨迹段 {segment.id} 评分: 峰值={peak_score:.3f}, 时长={duration_ratio:.3f}, "
                       f"稳定性={stability_score:.3f}, 最终={final_score:.3f}")

            return final_score
    
    def overlap_match(self, seg1, seg2, rtk_df: pd.DataFrame) -> float:
        """计算重叠区域匹配分数"""
        # 找到时间重叠区域
        overlap_start = max(seg1.start_time, seg2.start_time)
        overlap_end = min(seg1.end_time, seg2.end_time)
        
        if overlap_start >= overlap_end:
            return 0.0
        
        # 提取重叠区域的点
        seg1_overlap = [p for p in seg1.points 
                       if overlap_start <= p['timestamp'] <= overlap_end]
        seg2_overlap = [p for p in seg2.points 
                       if overlap_start <= p['timestamp'] <= overlap_end]
        
        if len(seg1_overlap) == 0 or len(seg2_overlap) == 0:
            return 0.0
        
        # 计算重叠区域的简单距离分数
        score = self._simple_overlap_score(seg1_overlap, seg2_overlap)
        
        logger.debug(f"重叠匹配: {seg1.id} vs {seg2.id} = {score:.3f}")
        
        return score
    
    def _simple_overlap_score(self, seg1_overlap, seg2_overlap):
        """计算重叠区域的简单距离分数"""
        if len(seg1_overlap) == 0 or len(seg2_overlap) == 0:
            return 0.0
        
        # 计算平均距离
        total_distance = 0
        count = 0
        
        for p1 in seg1_overlap:
            for p2 in seg2_overlap:
                distance = self.geo_utils.haversine_distance(
                    p1['lat'], p1['lon'], p2['lat'], p2['lon']
                )
                total_distance += distance
                count += 1
        
        if count == 0:
            return 0.0
        
        avg_distance = total_distance / count
        # 距离越小，分数越高
        score = max(0, 1 - avg_distance / 20.0)  # 20米内线性衰减
        return score
    
    def gap_match(self, seg1, seg2, rtk_df: pd.DataFrame) -> float:
        """计算间隙连接匹配分数"""
        if seg1.end_time >= seg2.start_time:
            return 0.0  # 没有间隙
        
        gap_duration = (seg2.start_time - seg1.end_time).total_seconds()
        
        if gap_duration > self.params.max_missing_gap:
            return 0.0  # 间隙太大
        
        # 获取间隙两端的RTK点
        gap_start_rtk = self.get_time_window_rtk(rtk_df, seg1.end_time, 1.0)
        gap_end_rtk = self.get_time_window_rtk(rtk_df, seg2.start_time, 1.0)
        
        if len(gap_start_rtk) == 0 or len(gap_end_rtk) == 0:
            return 0.0
        
        # 检查运动连续性
        if len(seg1.points) > 0 and len(seg2.points) > 0:
            end_point = seg1.points[-1]
            start_point = seg2.points[0]
            
            # 计算距离、速度差、航向差
            distance = GeoUtils.haversine_distance(
                end_point['lat'], end_point['lon'],
                start_point['lat'], start_point['lon']
            )
            
            speed_diff = abs(end_point['speed'] - start_point['speed'])
            heading_diff = abs(end_point['heading'] - start_point['heading'])
            if heading_diff > 180:
                heading_diff = 360 - heading_diff
            
            # 运动连续性评分
            distance_score = max(0, 1 - distance / self.params.switch_dist)
            speed_score = max(0, 1 - speed_diff / self.params.switch_speed)
            heading_score = max(0, 1 - heading_diff / self.params.switch_heading)
            
            continuity_score = (distance_score + speed_score + heading_score) / 3
            
            logger.debug(f"间隙匹配: {seg1.id} -> {seg2.id}, 间隙={gap_duration:.1f}s, "
                        f"连续性={continuity_score:.3f}")
            
            return continuity_score
        
        return 0.0 


    

    

    
    def _overlap_match_score(self, seg1, seg2):
        """计算重叠区域的匹配分数"""
        # 找到时间重叠区间
        overlap_start = max(seg1.start_time, seg2.start_time)
        overlap_end = min(seg1.end_time, seg2.end_time)
        
        if overlap_start >= overlap_end:
            return 0
        
        # 获取重叠时间段内的点
        seg1_overlap = [p for p in seg1.points 
                       if overlap_start <= p.timestamp <= overlap_end]
        seg2_overlap = [p for p in seg2.points 
                       if overlap_start <= p.timestamp <= overlap_end]
        
        if not seg1_overlap or not seg2_overlap:
            return 0
        
        # 使用简单距离计算重叠区域的匹配分数
        try:
            # 转换PerceptionPoint对象为字典格式
            seg1_dicts = []
            for point in seg1_overlap:
                if hasattr(point, 'timestamp'):  # PerceptionPoint对象
                    seg1_dicts.append({
                        'timestamp': point.timestamp,
                        'lat': point.lat,
                        'lon': point.lon,
                        'speed': point.speed,
                        'heading': point.heading
                    })
                else:  # 已经是字典格式
                    seg1_dicts.append(point)
            
            seg2_dicts = []
            for point in seg2_overlap:
                if hasattr(point, 'timestamp'):  # PerceptionPoint对象
                    seg2_dicts.append({
                        'timestamp': point.timestamp,
                        'lat': point.lat,
                        'lon': point.lon,
                        'speed': point.speed,
                        'heading': point.heading
                    })
                else:  # 已经是字典格式
                    seg2_dicts.append(point)
            
            simple_score = self._simple_overlap_score(seg1_dicts, seg2_dicts)
            return simple_score
        except Exception as e:
            print(f"重叠区域简单距离计算异常: {e}")
            return 0 


    
    def final_anomaly_summary(self, chain, rtk_start_time, rtk_end_time):
        """
        最终异常汇总 - 使用新的模块化异常分析系统
        """
        # 初始化异常分析管理器
        from .anomaly_analysis import AnomalyAnalysisManager

        # 明确标识使用新系统
        print("\n" + "="*80)
        print("🚀 使用新的模块化异常分析系统")
        print("="*80)

        # 构建配置字典
        config = self._build_anomaly_analysis_config()

        # 创建分析管理器
        analysis_manager = AnomalyAnalysisManager(config)

        # 执行所有异常分析
        results = analysis_manager.analyze_all(
            chain,
            rtk_start_time=rtk_start_time,
            rtk_end_time=rtk_end_time,
            rtk_points=self.rtk_points  # 传递RTK点数据给精度分析器
        )

        # 生成汇总报告
        summary = analysis_manager.generate_summary(results)

        # 打印汇总结果
        if hasattr(analysis_manager, 'result_summarizer'):
            analysis_manager.result_summarizer.print_summary(summary)
        else:
            self._print_simple_summary(summary)

        # 为了保持向后兼容，转换为原格式
        legacy_anomalies = self._convert_to_legacy_format(results)

        print("✅ 新异常分析系统执行完成")
        print("="*80)

        # 将完整的分析结果存储到实例变量中，供输出生成器使用
        self.analysis_results = results

        return legacy_anomalies

    def _build_anomaly_analysis_config(self) -> dict:
        """构建异常分析配置"""
        # 处理配置对象的兼容性
        if hasattr(self.params, '__dict__') and not callable(self.params):
            config_dict = self.params.__dict__
        elif hasattr(self.params, 'to_flat_dict') and not callable(self.params):
            config_dict = self.params.to_flat_dict()
        elif isinstance(self.params, dict):
            config_dict = self.params
        else:
            # 如果params是其他类型，使用默认配置
            config_dict = {
                'min_missing_gap': 0.5,
                'max_missing_gap': 5.0,
                'switch_dt': 2.0,
                'switch_dist': 10.0,
                'switch_speed': 5.0,
                'switch_heading': 30.0,
                'normal_detection_interval': 0.1
            }

        return config_dict

    def _convert_to_legacy_format(self, results: dict) -> dict:
        """转换为传统格式以保持向后兼容"""
        legacy_anomalies = {
            'split_events': [],
            'id_switches': [],
            'missing_gaps': [],
            'rejected_segments': []
        }

        for result in results.values():
            if not result.success:
                continue

            if result.analysis_type == 'split_detection':
                legacy_anomalies['split_events'] = result.events
            elif result.analysis_type == 'id_switch':
                legacy_anomalies['id_switches'] = result.events
            elif result.analysis_type == 'missing_gap':
                legacy_anomalies['missing_gaps'] = result.events

        return legacy_anomalies

    def _print_simple_summary(self, summary: dict):
        """打印简单汇总"""
        print("\n" + "="*60)
        print("🔍 异常分析结果汇总")
        print("="*60)
        print(f"分析器总数: {summary.get('total_analyzers', 0)}")
        print(f"成功执行: {summary.get('successful_analyzers', 0)}")
        print(f"检测事件总数: {summary.get('total_events', 0)}")
        print("="*60)



    def _construct_matched_dataframe(self, chain) -> pd.DataFrame:
        """
        从轨迹段构造匹配数据DataFrame，用于内部跳跃分析
        """
        try:
            rows = []
            for seg in chain:
                if hasattr(seg, 'points') and seg.points:
                    for point in seg.points:
                        # 处理PerceptionPoint对象或字典
                        if hasattr(point, 'timestamp'):  # PerceptionPoint对象
                            row = {
                                'perception_id': seg.id,
                                'per_timestamp': point.timestamp,
                                'perception_lat': point.lat,
                                'perception_lon': point.lon,
                                'perception_speed': point.speed,
                                'perception_heading': point.heading
                            }
                        else:  # 字典形式
                            row = {
                                'perception_id': seg.id,
                                'per_timestamp': point.get('timestamp', point.get('time')),
                                'perception_lat': point.get('lat'),
                                'perception_lon': point.get('lon'),
                                'perception_speed': point.get('speed'),
                                'perception_heading': point.get('heading')
                            }
                        rows.append(row)
            
            if not rows:
                return None
                
            df = pd.DataFrame(rows)
            # 确保时间戳格式正确
            if 'per_timestamp' in df.columns:
                df['per_timestamp'] = pd.to_datetime(df['per_timestamp'])
            
            return df
            
        except Exception as e:
            print(f"警告：构造匹配数据DataFrame失败: {e}")
            return None

    def roi_filter(self, perception_points):
        """ROI空间过滤"""
        filtered_points = []
        
        for per_point in perception_points:
            # 找到最近的RTK点
            min_distance = float('inf')
            nearest_rtk = None
            
            for rtk_point in self.rtk_points:
                # 计算时间差
                time_diff = abs((per_point.timestamp - rtk_point.timestamp).total_seconds())
                if time_diff > 10.0:  # 放宽到10秒
                    continue
                
                # 计算距离
                distance = self.geo_utils.haversine_distance(
                    per_point.lat, per_point.lon,
                    rtk_point.lat, rtk_point.lon
                )
                
                if distance < min_distance:
                    min_distance = distance
                    nearest_rtk = rtk_point
            
            # 检查是否在ROI内（放宽到100米）
            if nearest_rtk and min_distance <= 100.0:
                filtered_points.append(per_point)
        
        return filtered_points
    
    def corridor_filter(self, perception_points):
        """走廊空间过滤 - 新方法"""
        if self.corridor_filter is None:
            logger.warning("走廊过滤器不可用，回退到传统ROI过滤")
            return self.roi_filter(perception_points)
        
        try:
            logger.info("使用走廊过滤器进行空间过滤")
            filtered_points = self.corridor_filter.filter_perception_points(
                perception_points, self.rtk_points
            )
            
            # 获取过滤统计信息
            stats = self.corridor_filter.get_corridor_stats()
            logger.info(f"走廊过滤统计: {stats}")
            
            return filtered_points
            
        except Exception as e:
            logger.error(f"走廊过滤失败: {e}")
            if getattr(self.config, 'corridor_fallback_to_roi', True):
                logger.info("回退到传统ROI过滤")
                return self.roi_filter(perception_points)
            else:
                raise
    
    def filter_perception_points(self, perception_points, use_corridor=None):
        """
        统一的感知点过滤入口
        
        Args:
            perception_points: 感知数据点列表
            use_corridor: 是否使用走廊过滤，None表示根据配置决定
            
        Returns:
            过滤后的感知点列表
        """
        # 决定使用哪种过滤方法
        if use_corridor is None:
            use_corridor = getattr(self.config, 'corridor_enabled', True)
        
        # 添加详细调试信息
        logger.info(f"过滤方法选择调试: use_corridor={use_corridor}, corridor_filter is None={self.corridor_filter is None}")
        logger.info(f"配置检查: corridor_enabled={getattr(self.config, 'corridor_enabled', 'N/A')}")
        
        if use_corridor and self.corridor_filter is not None:
            logger.info("使用走廊过滤方法")
            return self.corridor_filter.filter_perception_points(perception_points, self.rtk_points)
        else:
            logger.info("使用传统ROI过滤方法")
            return self.roi_filter(perception_points)
    
    def build_segments(self, perception_points):
        """构建轨迹段"""
        from collections import defaultdict
        
        # 按ID分组
        id_groups = defaultdict(list)
        for point in perception_points:
            id_groups[point.id].append(point)
        
        segments = []
        for id_val, points in id_groups.items():
            if len(points) >= self.config.min_segment_length:
                # 按时间排序
                sorted_points = sorted(points, key=lambda x: x.timestamp)
                
                # 创建轨迹段
                segment = TrajectorySegment(
                    id=id_val,
                    points=sorted_points,
                    start_time=sorted_points[0].timestamp,
                    end_time=sorted_points[-1].timestamp
                )
                segments.append(segment)
        
        return segments
    
    def build_core_chain(self, segments):
        """构建核心链"""
        # 按时长降序排序
        sorted_segments = sorted(segments, key=lambda x: x.duration, reverse=True)
        
        chain = []
        remaining_segs = sorted_segments.copy()
        
        while remaining_segs:
            candidate = remaining_segs[0]
            
            # 获取竞争段
            competitors = self._get_competing_segments(candidate, remaining_segs)
            
            # 选择最佳段
            best_seg = self._enhanced_competition_selection(competitors)
            
            if best_seg.final_score >= self.config.local_match_thr:
                chain.append(best_seg)
                # 删除被覆盖的段
                remaining_segs = self._remove_covered_segments(remaining_segs, best_seg)
            else:
                remaining_segs.remove(candidate)
        
        return chain
    
    def _get_competing_segments(self, candidate_seg, remaining_segs):
        """获取竞争段 - 寻找完全在候选段时间范围内的其他ID段"""
        competitors = [candidate_seg]
        
        print(f"    候选段 {candidate_seg.id}: {candidate_seg.start_time} ~ {candidate_seg.end_time} (时长={candidate_seg.duration:.1f}s)")
        
        within_count = 0
        for seg in remaining_segs:
            if seg.id != candidate_seg.id and self._is_completely_within(seg, candidate_seg):
                competitors.append(seg)
                within_count += 1
                print(f"      竞争段 {seg.id}: {seg.start_time} ~ {seg.end_time} (时长={seg.duration:.1f}s)")
        
        print(f"    找到 {within_count} 个完全在时间范围内的竞争段，总竞争段数: {len(competitors)}")
        
        return competitors

    def _is_completely_within(self, seg, container_seg):
        """检查段是否完全在另一段的时间范围内"""
        return (seg.start_time >= container_seg.start_time and 
                seg.end_time <= container_seg.end_time)

    def _enhanced_competition_selection(self, competitors):
        """竞争选择 - 支持F1-style、统一评分和传统评分"""
        if self.scoring_method == 'f1_style':
            # 使用F1-style评分
            for seg in competitors:
                seg.final_score = self.calculate_f1_score(seg)
        elif self.scoring_method == 'unified':
            # 使用统一评分
            for seg in competitors:
                final_score, result = self.unified_scorer.unified_scoring(seg, self.rtk_points)
                seg.final_score = final_score
                seg.unified_result = result
                seg.scoring_strategy = result['strategy']
        else:
            # 使用传统评分方式
            max_duration = max(seg.duration for seg in competitors)

            for seg in competitors:
                # 计算各项分数
                peak_score = self._calculate_peak_score(seg)
                duration_ratio = min(seg.duration / max_duration, 1.0)
                stability_weight = self._calculate_stability_weight(seg)

                # 综合评分 - 兼容旧配置
                peak_weight = getattr(self.config, 'peak_weight', 0.6)
                duration_weight = getattr(self.config, 'duration_weight', 0.3)
                stability_weight_param = getattr(self.config, 'stability_weight', 0.1)

                seg.final_score = (
                    peak_score * peak_weight +
                    duration_ratio * duration_weight +
                    stability_weight * stability_weight_param
                )
        
        # 打印所有候选轨迹的评分详情
        print(f"    候选轨迹评分详情 (评分方法: {self.scoring_method}):")
        sorted_competitors = sorted(competitors, key=lambda x: x.final_score, reverse=True)
        for i, seg in enumerate(sorted_competitors):
            status = "🥇 胜出" if i == 0 else f"   第{i+1}名"

            if self.scoring_method == 'f1_style':
                # F1评分详细信息
                spatial_prec = getattr(seg, 'spatial_precision', 0)
                temporal_rec = getattr(seg, 'temporal_recall', 0)
                f1_score = getattr(seg, 'f1_score', 0)
                direction_cons = getattr(seg, 'direction_consistency', 0)
                print(f"      {status}: ID={seg.id}, 时长={seg.duration:.1f}s, 点数={len(seg.points)}, 最终分数={seg.final_score:.3f}")
                print(f"         └─ 空间精确率={spatial_prec:.3f}, 时间召回率={temporal_rec:.3f}, F1={f1_score:.3f}, 方向一致性={direction_cons:.3f}")
            elif self.scoring_method == 'unified':
                # 统一评分详细信息
                strategy = getattr(seg, 'scoring_strategy', 'unknown')
                # 从 unified_result 字典中安全获取详细指标，避免属性不存在导致打印为 0
                if hasattr(seg, 'unified_result') and isinstance(seg.unified_result, dict):
                    peak_quality = seg.unified_result.get('peak_quality', 0)
                    avg_quality = seg.unified_result.get('avg_quality', 0)
                    high_quality_segments = seg.unified_result.get('high_quality_segments', 0)
                else:
                    peak_quality = avg_quality = high_quality_segments = 0
                print(f"      {status}: ID={seg.id}, 时长={seg.duration:.1f}s, 点数={len(seg.points)}, 最终分数={seg.final_score:.3f}")
                print(f"         └─ 策略={strategy}, 峰值质量={peak_quality:.3f}, 平均质量={avg_quality:.3f}, 高质量段数={high_quality_segments}")
            else:
                # 传统评分信息
                print(f"      {status}: ID={seg.id}, 时长={seg.duration:.1f}s, 点数={len(seg.points)}, 分数={seg.final_score:.3f}")
        print()
        
        return max(competitors, key=lambda x: x.final_score)
    
    def _calculate_peak_score(self, seg):
        """计算峰值分数"""
        # 简化实现，使用基于距离的分数
        total_score = 0
        count = 0
        
        for per_point in seg.points:
            # 找到最近的RTK点
            min_distance = float('inf')
            for rtk_point in self.rtk_points:
                time_diff = abs((per_point.timestamp - rtk_point.timestamp).total_seconds())
                if time_diff <= 1.0:  # 1秒内的点
                    distance = self.geo_utils.haversine_distance(
                        per_point.lat, per_point.lon,
                        rtk_point.lat, rtk_point.lon
                    )
                    min_distance = min(min_distance, distance)
            
            if min_distance < float('inf'):
                # 距离越小，分数越高
                score = max(0, 1 - min_distance / 20.0)  # 20米内线性衰减
                total_score += score
                count += 1
        
        return total_score / count if count > 0 else 0
    
    def _calculate_stability_weight(self, seg):
        """计算稳定性权重"""
        good_points = 0
        total_points = len(seg.points)
        
        for per_point in seg.points:
            # 找到最近的RTK点
            min_distance = float('inf')
            for rtk_point in self.rtk_points:
                time_diff = abs((per_point.timestamp - rtk_point.timestamp).total_seconds())
                if time_diff <= 1.0:
                    distance = self.geo_utils.haversine_distance(
                        per_point.lat, per_point.lon,
                        rtk_point.lat, rtk_point.lon
                    )
                    min_distance = min(min_distance, distance)
            
            if min_distance < 10.0:  # 10米内认为是好点
                good_points += 1
        
        return good_points / total_points if total_points > 0 else 0
    
    # ==================== F1-Style 评分方法 ====================
    
    def calculate_f1_score(self, segment):
        """计算F1-style综合评分"""
        if self.scoring_method != 'f1_style':
            # 回退到传统评分
            return self._calculate_peak_score(segment)
        
        # 1. 空间精确率 (Precision)
        spatial_precision = self._calculate_spatial_precision(segment)
        
        # 2. 时间召回率 (Recall)  
        temporal_recall = self._calculate_temporal_recall(segment)
        
        # 3. F1分数：调和平均
        if spatial_precision + temporal_recall == 0:
            f1_score = 0
        else:
            f1_score = 2 * (spatial_precision * temporal_recall) / (spatial_precision + temporal_recall)
        
        # 4. 方向一致性检查（用于过滤旁边车辆）
        direction_consistency = self._calculate_direction_consistency(segment)
        
        # 5. 综合评分
        final_score = (
            f1_score * getattr(self.config, 'f1_weight', 0.8) +
            direction_consistency * getattr(self.config, 'direction_weight', 0.2)
        )
        
        logger.debug(f"轨迹段 {segment.id} F1评分: 空间精确率={spatial_precision:.3f}, "
                    f"时间召回率={temporal_recall:.3f}, F1={f1_score:.3f}, "
                    f"方向一致性={direction_consistency:.3f}, 最终={final_score:.3f}")
        
        # 保存详细评分信息到segment对象，用于竞争选择时打印
        segment.spatial_precision = spatial_precision
        segment.temporal_recall = temporal_recall
        segment.f1_score = f1_score
        segment.direction_consistency = direction_consistency
        
        return final_score
    
    def _calculate_spatial_precision(self, segment):
        """计算空间精确率：基于距离的匹配质量"""
        total_score = 0
        count = 0
        decay_distance = getattr(self.config, 'spatial_decay_distance', 5.0)
        
        for per_point in segment.points:
            # 找到最近的RTK点（时间窗口内）
            min_distance = self._find_nearest_rtk_distance(per_point)
            
            if min_distance < float('inf'):
                # 使用指数衰减：距离越近分数越高
                score = math.exp(-min_distance / decay_distance)
                total_score += score
                count += 1
        
        return total_score / count if count > 0 else 0
    
    def _calculate_temporal_recall(self, segment):
        """计算时间召回率：相对时长，避免过度偏向长轨迹"""
        max_duration = getattr(self.config, 'max_reasonable_duration', 30.0)
        normalized_duration = min(segment.duration / max_duration, 1.0)
        
        # 使用平方根避免时长优势过于明显
        return math.sqrt(normalized_duration)
    
    def _calculate_direction_consistency(self, segment):
        """计算方向一致性：防止旁边平行车辆获得高分"""
        if len(segment.points) < 3:
            return 0.5  # 点数太少，给中等分数
        
        # 计算感知轨迹的整体方向向量
        perception_direction = self._get_trajectory_direction(segment.points)
        
        # 计算对应时间段RTK轨迹的方向向量
        rtk_direction = self._get_rtk_direction_in_timerange(segment.start_time, segment.end_time)
        
        if perception_direction is None or rtk_direction is None:
            return 0.5
        
        # 计算方向向量夹角
        dot_product = np.dot(perception_direction, rtk_direction)
        dot_product = np.clip(dot_product, -1.0, 1.0)
        
        angle_rad = np.arccos(abs(dot_product))  # 使用绝对值，考虑反向
        angle_deg = np.degrees(angle_rad)
        
        # 角度阈值检查
        threshold = getattr(self.config, 'direction_threshold_degrees', 45.0)
        if angle_deg <= threshold:
            # 角度越小，分数越高
            return 1.0 - (angle_deg / threshold) * 0.5
        else:
            # 角度过大，大幅降分
            return max(0.1, 0.5 - (angle_deg - threshold) / 90.0)
    
    def _find_nearest_rtk_distance(self, per_point):
        """找到感知点最近的RTK点距离（时间窗口内）"""
        min_distance = float('inf')
        
        for rtk_point in self.rtk_points:
            # 时间窗口过滤（1秒内）
            time_diff = abs((per_point.timestamp - rtk_point.timestamp).total_seconds())
            if time_diff <= 1.0:
                distance = self.geo_utils.haversine_distance(
                    per_point.lat, per_point.lon,
                    rtk_point.lat, rtk_point.lon
                )
                min_distance = min(min_distance, distance)
        
        return min_distance
    
    def _get_trajectory_direction(self, points):
        """计算轨迹的整体方向向量"""
        if len(points) < 2:
            return None
        
        # 使用起点和终点计算整体方向
        start_point = points[0]
        end_point = points[-1]
        
        # 转换为UTM坐标计算方向
        start_x, start_y, _ = self.geo_utils.wgs84_to_utm(start_point.lat, start_point.lon)
        end_x, end_y, _ = self.geo_utils.wgs84_to_utm(end_point.lat, end_point.lon)
        
        direction = np.array([end_x - start_x, end_y - start_y])
        norm = np.linalg.norm(direction)
        
        if norm > 0:
            return direction / norm
        else:
            return None
    
    def _get_rtk_direction_in_timerange(self, start_time, end_time):
        """获取指定时间范围内RTK轨迹的方向向量"""
        # 找到时间范围内的RTK点
        rtk_in_range = [
            p for p in self.rtk_points 
            if start_time <= p.timestamp <= end_time
        ]
        
        if len(rtk_in_range) < 2:
            # 扩展时间范围
            buffer_time = timedelta(seconds=2)
            rtk_in_range = [
                p for p in self.rtk_points 
                if (start_time - buffer_time) <= p.timestamp <= (end_time + buffer_time)
            ]
        
        if len(rtk_in_range) < 2:
            return None
        
        # 使用起点和终点计算方向
        start_rtk = rtk_in_range[0]
        end_rtk = rtk_in_range[-1]
        
        start_x, start_y, _ = self.geo_utils.wgs84_to_utm(start_rtk.lat, start_rtk.lon)
        end_x, end_y, _ = self.geo_utils.wgs84_to_utm(end_rtk.lat, end_rtk.lon)
        
        direction = np.array([end_x - start_x, end_y - start_y])
        norm = np.linalg.norm(direction)
        
        if norm > 0:
            return direction / norm
        else:
            return None

    def _remove_covered_segments(self, remaining_segs, matched_seg):
        """删除被覆盖的段"""
        filtered_segs = []
        
        for seg in remaining_segs:
            if not self._is_completely_covered(seg, matched_seg):
                filtered_segs.append(seg)
        
        return filtered_segs
    
    def _is_completely_covered(self, seg, matched_seg):
        """检查段是否完全被覆盖"""
        return (seg.start_time >= matched_seg.start_time and 
                seg.end_time <= matched_seg.end_time)
    
    def _haversine_distance(self, lat1, lon1, lat2, lon2):
        """计算haversine距离"""
        return self.geo_utils.haversine_distance(lat1, lon1, lat2, lon2)

class TrajectorySegment:
    """轨迹段类"""
    
    def __init__(self, id, points, start_time, end_time):
        self.id = id
        self.points = points
        self.start_time = start_time
        self.end_time = end_time
        self.duration = (end_time - start_time).total_seconds()
        self.final_score = 0
        self.peak_score = 0
        self.duration_ratio = 0
        self.stability_weight = 0 
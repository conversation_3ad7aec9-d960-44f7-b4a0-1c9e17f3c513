# 车路协同轨迹匹配工具

一个智能的车路协同感知轨迹匹配系统，支持高精度轨迹匹配、异常检测和精度分析，能够精准匹配RTK轨迹与感知数据。

## ✨ 主要功能

- 🎯 **高精度轨迹匹配**: 基于时空相关性的智能匹配算法
- 🔍 **异常检测分析**: 多维度异常检测（分裂、ID切换、漏检、时间跳跃等）
- 📊 **精度分析**: 定位、速度、航向精度的量化评估
- 📈 **可视化报告**: 生成详细的HTML、CSV、JSON格式报告
- ⚙️ **灵活配置**: 支持多种配置模式和参数调优

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 基本使用
```bash
# 标准轨迹匹配（推荐）
python main.py --rtk data/rtk_part005.txt --perception data/AJ06993PAJ00115B1.txt --config config/unified_config.json

# 使用默认配置
python main.py --rtk data/rtk_part005.txt --perception data/AJ06993PAJ00115B1.txt --config config/default.json

# 详细输出模式
python main.py --rtk data/rtk_part005.txt --perception data/AJ06993PAJ00115B1.txt --config config/unified_config.json --verbose

# 指定输出目录
python main.py --rtk data/rtk_part005.txt --perception data/AJ06993PAJ00115B1.txt --output output/my_results/
```

### 3. 查看结果
```bash
# 结果文件位于 output/results/ 目录下
# - CSV格式: 匹配结果和异常统计
# - JSON格式: 详细诊断信息
# - HTML格式: 可视化报告（如果启用精度分析）
```

## 📁 项目结构

```
multi_object_data_recall/
├── 📁 core/                    # 核心模块
│   ├── anomaly_analysis/      # 异常分析模块
│   │   ├── accuracy_analyzer.py      # 精度分析器
│   │   ├── analysis_manager.py       # 分析管理器
│   │   ├── base_analyzer.py          # 基础分析器
│   │   ├── id_switch_analyzer.py     # ID切换检测
│   │   ├── missing_gap_analyzer.py   # 漏检分析
│   │   ├── result_summarizer.py      # 结果汇总
│   │   ├── split_detector.py         # 分裂检测
│   │   ├── time_jump_analyzer.py     # 时间跳跃检测
│   │   └── trajectory_quality_analyzer.py # 轨迹质量分析
│   ├── config_loader.py       # 配置加载器
│   ├── corridor_filter.py     # 走廊过滤器
│   ├── data_utils.py          # 数据处理工具
│   ├── gap_analyzer.py        # 间隙分析器
│   ├── output_generator.py    # 输出生成器
│   ├── preprocessor.py        # 数据预处理器
│   ├── report_generator.py    # 报告生成器
│   ├── simple_distance_matcher.py # 距离匹配器
│   └── trajectory_matcher.py  # 轨迹匹配器
├── 📁 config/                  # 配置文件
│   ├── default.json           # 默认配置
│   └── unified_config.json    # 统一配置（推荐）
├── 📁 data/                    # 测试数据
│   ├── rtk_part005.txt        # RTK参考轨迹
│   └── AJ06993PAJ00115B1.txt  # 感知数据样例
├── 📁 docs/                    # 文档
│   ├── config_reference.md    # 配置参考
│   ├── gap_analysis_usage_example.md # 使用示例
│   └── scoring_system_guide.md # 评分系统指南
├── 📁 output/                  # 输出目录
│   ├── results/               # 匹配结果
│   ├── reports/               # 分析报告
│   └── visualizations/        # 可视化文件
├── 📁 templates/               # 报告模板
│   └── accuracy_report_template.html # 精度分析报告模板
├── 📁 tests/                   # 测试文件
│   └── test_accuracy_report_integration.py # 精度报告集成测试
├── main.py                     # 主程序入口
├── requirements.txt            # 依赖列表
└── README.md                   # 项目说明
```

## ✨ 核心功能

### 🎯 轨迹匹配
- **智能匹配算法**: 基于时空相关性的高精度匹配
- **多评分策略**: 支持统一评分和传统评分模式
- **数据格式支持**: RTK和感知数据的多种格式兼容

### 🔍 异常检测
- **分裂检测**: 自动识别轨迹分裂事件
- **ID切换检测**: 检测目标ID异常切换
- **漏检分析**: 分析轨迹缺失和间隙
- **时间跳跃检测**: 识别时间序列异常
- **轨迹质量评估**: 综合质量评分和分析

### 📊 精度分析
- **定位精度**: 与RTK参考轨迹的位置误差分析
- **速度精度**: 速度测量精度评估
- **航向精度**: 航向角度误差分析
- **统计分析**: 均值、标准差、分位数等统计指标

### 📈 报告生成
- **HTML报告**: 可视化精度分析报告，包含详细数据表格
- **CSV输出**: 匹配结果和统计数据
- **JSON格式**: 完整的诊断信息和元数据

## 📊 配置说明

### 配置文件
- **unified_config.json**: 推荐配置，包含完整的异常检测和精度分析功能
- **default.json**: 基础配置，仅包含核心匹配功能

### 主要配置项
- **anomaly_analysis**: 异常检测配置
- **accuracy_analysis**: 精度分析配置
- **matching_parameters**: 匹配算法参数
- **output_settings**: 输出格式设置

详细配置说明请参考 `docs/config_reference.md`

## 🔧 使用示例

### 基础轨迹匹配
```bash
# 基础匹配
python main.py --rtk data/rtk_part005.txt --perception data/AJ06993PAJ00115B1.txt --config config/unified_config.json

# 启用详细输出
python main.py --rtk data/rtk_part005.txt --perception data/AJ06993PAJ00115B1.txt --config config/unified_config.json --verbose
```

### 精度分析报告
```bash
# 启用精度分析（需要RTK参考数据）
python main.py --rtk data/rtk_part005.txt --perception data/AJ06993PAJ00115B1.txt --config config/unified_config.json

# 查看HTML报告
# 报告位于: output/results/accuracy_analysis_report.html
```

### 输出文件说明
- **CSV文件**: 匹配结果和统计数据
- **JSON文件**: 完整的诊断信息和异常检测结果
- **HTML文件**: 精度分析可视化报告（如果启用精度分析）

## 📊 数据格式

### RTK数据格式
```
timestamp,lat,lon,speed,heading
2025-02-25T10:06:10.881000+00:00,30.4628667119991,114.12295355827987,0.0,244.09442138671875
```
- **速度单位**: 原始NMEA数据为节(knots)，自动转换为米/秒(m/s)

### 感知数据格式
```
timestamp,id,lat,lon,speed,heading
2025-02-25 18:06:10.881,102215,30.4628667119991,114.12295355827987,0.0,244.09442138671875
```
- **速度单位**: 原始JSON数据为公里/小时(km/h)，自动转换为米/秒(m/s)

## 📈 输出结果

### 匹配结果CSV
- RTK轨迹与感知数据的逐点匹配
- 包含时间戳、位置、速度、航向等信息

### 诊断报告JSON
- 异常检测结果（分裂、ID切换、漏检等）
- 精度分析统计（如果启用）
- 匹配质量评估

### 精度分析HTML报告
- 可视化精度分析结果
- 详细数据表格，显示每个时间点的误差
- 统计图表和质量评估

## 📚 文档

- **[配置参考](docs/config_reference.md)** - 完整配置说明
- **[使用示例](docs/gap_analysis_usage_example.md)** - 详细使用示例
- **[评分系统指南](docs/scoring_system_guide.md)** - 评分策略说明

## 🔧 开发说明

### 核心模块
- **core/anomaly_analysis/**: 异常检测分析模块
- **core/data_utils.py**: 数据加载和处理
- **core/simple_distance_matcher.py**: 轨迹匹配核心算法
- **core/output_generator.py**: 结果输出生成
- **core/report_generator.py**: 报告生成器

### 扩展开发
1. **新增分析器**: 继承`BaseAnomalyAnalyzer`类
2. **配置管理**: 修改`config/unified_config.json`
3. **报告模板**: 自定义`templates/`目录下的HTML模板

## 📄 许可证

本项目采用MIT许可证